uuid: 7e886eb8-8e79-4b07-9a2c-552a33afbcad
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_st2_access
    - node.type.branch
    - paragraphs.paragraphs_type.access_axons
    - paragraphs.paragraphs_type.access_neurons
  module:
    - entity_reference_revisions
id: node.branch.field_st2_access
field_name: field_st2_access
entity_type: node
bundle: branch
label: Access
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      access_axons: access_axons
      access_neurons: access_neurons
    negate: 0
    target_bundles_drag_drop:
      access_axons:
        weight: 51
        enabled: true
      access_money:
        weight: 52
        enabled: false
      access_neurons:
        weight: 53
        enabled: true
      access_test:
        weight: 54
        enabled: false
      access_test_question:
        weight: 55
        enabled: false
      animation:
        weight: 56
        enabled: false
      big5:
        weight: 57
        enabled: false
      big5_results:
        weight: 58
        enabled: false
      bubble_quiz:
        weight: 59
        enabled: false
      certifications:
        weight: 60
        enabled: false
      course:
        weight: 61
        enabled: false
      dream_job:
        weight: 63
        enabled: false
      education_student:
        weight: 64
        enabled: false
      explanatotry_note:
        weight: 65
        enabled: false
      factor:
        weight: 66
        enabled: false
      feed_alpha_gen:
        weight: 68
        enabled: false
      feed_big5:
        weight: 69
        enabled: false
      feed_coming_soon:
        weight: 70
        enabled: false
      feed_course:
        weight: 71
        enabled: false
      feed_nano_boost:
        weight: 73
        enabled: false
      feed_nano_boost_project:
        weight: 74
        enabled: false
      feed_ugc_by_brayn:
        weight: 75
        enabled: false
      feeds:
        weight: 67
        enabled: false
      infographic:
        weight: 76
        enabled: false
      infographic_slider:
        weight: 77
        enabled: false
      infographic_slider_images:
        weight: 78
        enabled: false
      multioption_qustion:
        weight: 79
        enabled: false
      offer:
        weight: 80
        enabled: false
      onboarding_special_offers:
        weight: 81
        enabled: false
      quiz_crossword:
        weight: 82
        enabled: false
      quiz_crossword_letter:
        weight: 83
        enabled: false
      quiz_image:
        weight: 84
        enabled: false
      quiz_image_question:
        weight: 85
        enabled: false
      quiz_tinder_mechanics:
        weight: 86
        enabled: false
      quiz_tinder_mechanics_question:
        weight: 87
        enabled: false
      quiz_video:
        weight: 88
        enabled: false
      quiz_video_question:
        weight: 89
        enabled: false
      radar:
        weight: 90
        enabled: false
      results:
        weight: 91
        enabled: false
      reward:
        weight: 92
        enabled: false
      sign_up:
        weight: 93
        enabled: false
      skill_course:
        weight: 94
        enabled: false
      skill_student:
        weight: 95
        enabled: false
      smart:
        weight: 97
        enabled: false
      video:
        weight: 98
        enabled: false
      video_task:
        weight: 99
        enabled: false
      work_student:
        weight: 100
        enabled: false
field_type: entity_reference_revisions
