uuid: 379ec29e-ef00-463a-a5a6-7fbd8af80a64
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_sz_skills
    - node.type.shortzzz
    - paragraphs.paragraphs_type.skill_course
  module:
    - entity_reference_revisions
id: node.shortzzz.field_sz_skills
field_name: field_sz_skills
entity_type: node
bundle: shortzzz
label: Skills
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      skill_course: skill_course
    negate: 0
    target_bundles_drag_drop:
      access_axons:
        weight: 21
        enabled: false
      access_money:
        weight: 22
        enabled: false
      access_neurons:
        weight: 23
        enabled: false
      access_test:
        weight: 24
        enabled: false
      access_test_question:
        weight: 25
        enabled: false
      ai_text_chat:
        weight: 58
        enabled: false
      ai_voice_chat:
        weight: 59
        enabled: false
      animation:
        weight: 60
        enabled: false
      big5:
        weight: 61
        enabled: false
      big5_results:
        weight: 62
        enabled: false
      bubble_quiz:
        weight: 63
        enabled: false
      certifications:
        weight: 64
        enabled: false
      course:
        weight: 65
        enabled: false
      dream_job:
        weight: 66
        enabled: false
      education_student:
        weight: 26
        enabled: false
      explanatotry_note:
        weight: 29
        enabled: false
      factor:
        weight: 30
        enabled: false
      feed_alpha_gen:
        weight: 71
        enabled: false
      feed_big5:
        weight: 72
        enabled: false
      feed_coming_soon:
        weight: 73
        enabled: false
      feed_course:
        weight: 74
        enabled: false
      feed_nano_boost:
        weight: 76
        enabled: false
      feed_nano_boost_project:
        weight: 77
        enabled: false
      feed_ugc_by_brayn:
        weight: 78
        enabled: false
      feeds:
        weight: 70
        enabled: false
      infographic:
        weight: 27
        enabled: false
      infographic_slider:
        weight: 80
        enabled: false
      infographic_slider_images:
        weight: 81
        enabled: false
      multioption_qustion:
        weight: 82
        enabled: false
      offer:
        weight: 83
        enabled: false
      onboarding_special_offers:
        weight: 84
        enabled: false
      quiz_crossword:
        weight: 28
        enabled: false
      quiz_crossword_letter:
        weight: 29
        enabled: false
      quiz_image:
        weight: 30
        enabled: false
      quiz_image_question:
        weight: 31
        enabled: false
      quiz_tinder_mechanics:
        weight: 32
        enabled: false
      quiz_tinder_mechanics_question:
        weight: 33
        enabled: false
      quiz_video:
        weight: 34
        enabled: false
      quiz_video_question:
        weight: 35
        enabled: false
      radar:
        weight: 93
        enabled: false
      results:
        weight: 94
        enabled: false
      reward:
        weight: 95
        enabled: false
      set_smart_goal:
        weight: 96
        enabled: false
      sign_up:
        weight: 97
        enabled: false
      skill_course:
        weight: 36
        enabled: true
      skill_student:
        weight: 37
        enabled: false
      smart:
        weight: 100
        enabled: false
      smart_goal:
        weight: 101
        enabled: false
      video:
        weight: 39
        enabled: false
      video_task:
        weight: 103
        enabled: false
      work_student:
        weight: 40
        enabled: false
field_type: entity_reference_revisions
