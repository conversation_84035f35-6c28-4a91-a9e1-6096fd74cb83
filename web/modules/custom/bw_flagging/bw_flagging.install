<?php

/**
 * @file
 * Install-related hooks.
 */

/**
 * Fill the flagging tables.
 */
function bw_flagging_install() {
  $flags = \Drupal::database()
    ->select('flagging')
    ->fields('flagging',
      [
        'flag_id',
        'entity_type',
        'entity_id',
        'uid',
        'created',
      ]
    )
    ->execute()
    ->fetchAll(\PDO::FETCH_ASSOC);
  foreach ($flags as $flag) {
    $flagging_entity_type = "flag_{$flag['flag_id']}";

    if ('shortzzz_project' === $flag['entity_type']) {
      $flag_type = end(explode('_', $flag['flag_id']));
      $flagging_entity_type = "flag_shortzzz_proj_{$flag_type}";
    }

    \Drupal::service('bw_flagging.flag')->flag(
      $flagging_entity_type,
      (int) $flag['entity_id'],
      (int) $flag['uid'],
      (int) $flag['created'],
    );
  }
}

/**
 * Install Lesson stats entities.
 */
function bw_flagging_update_10001() {
  $entity_update_manager = \Drupal::entityDefinitionUpdateManager();

  $flags = [
    'flag_c_course_less_favourite',
    'flag_c_course_less_view',
    'flag_c_course_less_like',
    'flag_c_course_less_share',
    'flag_course_less_favourite',
    'flag_course_less_view',
    'flag_course_less_like',
    'flag_course_less_share',
  ];

  foreach ($flags as $flag) {
    $entity_update_manager->installEntityType(
      \Drupal::entityTypeManager()->getDefinition($flag)
    );
  }
}

/**
 * Install Lesson stats entities.
 */
function bw_flagging_update_10004() {
  $entity_update_manager = \Drupal::entityDefinitionUpdateManager();

  $flags = [
    'flag_alpha_gen_checked',
    'flag_alpha_gen_like',
    'flag_alpha_gen_share',
    'flag_alpha_gen_view',
    'flag_c_alpha_gen_checked',
    'flag_c_alpha_gen_like',
    'flag_c_alpha_gen_share',
    'flag_c_alpha_gen_view',
    'flag_coming_soon_checked',
    'flag_coming_soon_like',
    'flag_coming_soon_share',
    'flag_coming_soon_view',
    'flag_c_coming_soon_checked',
    'flag_c_coming_soon_like',
    'flag_c_coming_soon_share',
    'flag_c_coming_soon_view',
  ];

  foreach ($flags as $flag) {
    $entity_update_manager->installEntityType(
      \Drupal::entityTypeManager()->getDefinition($flag)
    );
  }
}

/**
 * Install flag entities.
 */
function bw_flagging_update_10005() {
  $entity_update_manager = \Drupal::entityDefinitionUpdateManager();

  $flags = [
    'flag_big5_view',
    'flag_shortzzz_view',
    'flag_c_big5_view',
    'flag_c_shortzzz_view',
  ];

  foreach ($flags as $flag) {
    $entity_update_manager->installEntityType(
      \Drupal::entityTypeManager()->getDefinition($flag)
    );
  }
}

/**
 * Install flag entities.
 */
function bw_flagging_update_10006() {
  $entity_update_manager = \Drupal::entityDefinitionUpdateManager();

  $flags = [
    'flag_ugc_by_brayn_view',
    'flag_ugc_by_brayn_like',
    'flag_c_ugc_by_brayn_view',
    'flag_c_ugc_by_brayn_like',
  ];

  foreach ($flags as $flag) {
    $entity_update_manager->installEntityType(
      \Drupal::entityTypeManager()->getDefinition($flag)
    );
  }
}

/**
 * Install Video Component flag entities.
 */
function bw_flagging_update_10007() {
  $entity_update_manager = \Drupal::entityDefinitionUpdateManager();

  $flags = [
    'flag_video_component_like',
    'flag_video_component_share',
    'flag_video_component_favourite',
    'flag_video_component_view',
    'flag_c_vc_like',
    'flag_c_vc_share',
    'flag_c_vc_favourite',
    'flag_c_vc_view',
  ];

  foreach ($flags as $flag) {
    $entity_update_manager->installEntityType(
      \Drupal::entityTypeManager()->getDefinition($flag)
    );
  }
}
