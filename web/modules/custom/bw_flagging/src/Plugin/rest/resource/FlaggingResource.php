<?php

namespace Drupal\bw_flagging\Plugin\rest\resource;

use <PERSON><PERSON>al\bw_flagging\FlagService;
use <PERSON><PERSON>al\bw_notifications\NotificationService;
use <PERSON>upal\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Session\AccountInterface;
use Drupal\rest\ModifiedResourceResponse;
use Drupal\rest\Plugin\ResourceBase;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * Flag/unflag of entities.
 *
 * @RestResource(
 *   id = "bw_flagging",
 *   label = @Translation("BW: Flagging"),
 *   uri_paths = {
 *     "create" = "/api/{version}/flagging",
 *     "canonical" = "/api/{version}/flagging/{flag_type}/{entity_id}",
 *   },
 * )
 */
final class FlaggingResource extends ResourceBase {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * The flag service.
   *
   * @var \Drupal\bw_flagging\FlagService
   */
  private FlagService $flag;

  /**
   * Notification service.
   *
   * @var \Drupal\bw_notifications\NotificationService
   */
  protected NotificationService $notification;

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  private AccountInterface $currentUser;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param array $serializer_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   A logger instance.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\bw_flagging\FlagService $flag
   *   The flag service.
   * @param \Drupal\bw_notifications\NotificationService $notification
   *   Notification service.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    array $serializer_formats,
    LoggerInterface $logger,
    EntityTypeManagerInterface $entity_type_manager,
    FlagService $flag,
    NotificationService $notification,
    AccountInterface $current_user,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
    $this->logger = $logger;
    $this->entityTypeManager = $entity_type_manager;
    $this->flag = $flag;
    $this->notification = $notification;
    $this->currentUser = $current_user;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('entity_type.manager'),
      $container->get('bw_flagging.flag'),
      $container->get('bw_notification'),
      $container->get('current_user'),
    );
  }

  /**
   * Unflag entity.
   *
   * @param string $flag_type
   *   The flag type.
   * @param string $entity_id
   *   The entity id.
   *
   * @return \Drupal\rest\ModifiedResourceResponse
   *   The HTTP response object.
   */
  public function delete(string $flag_type, string $entity_id): ModifiedResourceResponse {
    $this->flag->unflag(
      $this->getFlaggingEntityType($flag_type),
      (int) $entity_id,
      (int) $this->currentUser->id()
    );

    return new ModifiedResourceResponse(NULL, 204);
  }

  /**
   * Flag entity.
   *
   * @param \Symfony\Component\HttpFoundation\Request $request
   *   The HTTP request object.
   *
   * @return \Drupal\rest\ModifiedResourceResponse
   *   The HTTP response object.
   */
  public function post(Request $request): ModifiedResourceResponse {
    $request_data = json_decode($request->getContent(), TRUE);
    if (!($flag_type = $request_data['flag_type'] ?? NULL)) {
      throw new BadRequestHttpException('The flag type is missing.');
    }

    // Check if the flag_type contains any of the types requiring an entity_id.
    $requires_entity = FALSE;
    foreach (['coming_soon', 'alpha_gen', 'big5', 'shortzzz'] as $type) {
      if (str_contains($flag_type, $type)) {
        $requires_entity = TRUE;
        break;
      }
    }

    // If an entity_id is required but missing, throw an exception.
    if ($requires_entity && !($entity_id = $request_data['entity_id'] ?? NULL)) {
      throw new BadRequestHttpException('The entity id is missing.');
    }

    $this->flag->flag(
      $this->getFlaggingEntityType($flag_type),
      (int) $entity_id,
      (int) $this->currentUser->id()
    );

    if ('shortzzz_project_like' === $flag_type) {
      $this->shortzzzProjectLikeFlagging((int) $entity_id);
    }

    if ('student_follow' === $flag_type) {
      $this->studentFollowFlagging((int) $entity_id);
    }

    return new ModifiedResourceResponse(['success' => TRUE], 201);
  }

  /**
   * Validates the request data and returns the needed flag entity type.
   *
   * @param string $flag_type
   *   The flag type.
   *
   * @return string
   *   The flagging entity type.
   */
  private function getFlaggingEntityType(string $flag_type): string {
    return strtr(
      "flag_{$flag_type}",
      [
        'shortzzz_project' => 'shortzzz_proj',
      ],
    );
  }

  /**
   * Shortzzz project like flagging additional steps.
   *
   * @param int $shortzzz_project_id
   *   The flagged shortzzz project id.
   */
  private function shortzzzProjectLikeFlagging(int $shortzzz_project_id): void {
    $shortzzz_project = $this->entityTypeManager
      ->getStorage('shortzzz_project')
      ->load($shortzzz_project_id);
    if (!$shortzzz_project) {
      return;
    }

    $user = $shortzzz_project->getOwner();

    $this->notification->send(
      $this->entityTypeManager
        ->getStorage('profile')
        ->loadByUser($user, 'student'),
      [
        'contents' => ['en' => $this->t("🌟 The likes keep rolling in—you’re an awesome content creator!")],
        'include_external_user_ids' => [$user->uuid()],
        'data' => [
          'type' => 'shortzzz_project_like',
          'shortzzz_project_id' => $shortzzz_project->id(),
        ],
      ]
    );
  }

  /**
   * Student follow flagging additional steps.
   *
   * @param int $user_id
   *   The flagged user id.
   */
  private function studentFollowFlagging(int $user_id): void {
    $user = $this->entityTypeManager->getStorage('user')->load($user_id);
    $this->notification->send(
      $this->entityTypeManager
        ->getStorage('profile')
        ->loadByUser($user, 'student'),
      [
        'contents' => ['en' => $this->t("🦋 New follower! Your squad’s growing 🤝")],
        'include_external_user_ids' => [$user->uuid()],
        'data' => [
          'type' => 'follower',
          'user_id' => $this->currentUser->id(),
        ],
      ]
    );
  }

}
