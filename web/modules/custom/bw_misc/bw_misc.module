<?php

/**
 * @file
 * Module-related hooks.
 */

use Drupal\Core\Access\AccessResult;
use Drupal\Core\Cache\Cache;
use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Entity\EntityStorageInterface;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\Site\Settings;
use Drupal\Core\StringTranslation\TranslatableMarkup;
use Drupal\paragraphs\Entity\Paragraph;
use Drupal\paragraphs\ParagraphInterface;

/**
 * Implements hook_cron().
 */
function bw_misc_cron() {
  $queue = \Drupal::queue('bw_misc_feed_queue');

  $nids = \Drupal::entityTypeManager()
    ->getStorage('node')
    ->getQuery()
    ->condition('status', 1)
    ->condition('type', _bw_misc_get_course_node_types(), 'IN')
    ->accessCheck(FALSE)
    ->execute();
  foreach ($nids as $nid) {
    $queue->createItem(['type' => 'course', 'id' => $nid]);
  }

  $sids = \Drupal::entityTypeManager()
    ->getStorage('node')
    ->getQuery()
    ->condition('status', 1)
    ->condition('type', 'shortzzz')
    ->accessCheck(FALSE)
    ->execute();
  foreach ($sids as $sid) {
    $queue->createItem(['type' => 'shortzzz', 'id' => $sid]);
  }
}

/**
 * Set dynamic allowed values for the interests field.
 *
 * @return array
 *   An array of possible key and value options.
 *
 * @see options_allowed_values()
 */
function bw_misc_interests_allowed_values_function(): array {
  return [
    'self_concept' => new TranslatableMarkup('Self-concept'),
    'emotional_intelligence' => new TranslatableMarkup('Emotional intelligence'),
    'resilience_and_stress_management' => new TranslatableMarkup('Resilience'),
    'self_organization' => new TranslatableMarkup('Self-organization'),
    'strategic_thinking' => new TranslatableMarkup('Strategic thinking'),
    'critical_thinking' => new TranslatableMarkup('Critical thinking'),
    'systemic_thinking' => new TranslatableMarkup('Systemic thinking'),
    'lifestyle_thinking' => new TranslatableMarkup('Lifestyle thinking'),
    'design_thinking' => new TranslatableMarkup('Design thinking'),
    'communication' => new TranslatableMarkup('Communication'),
    'leadership' => new TranslatableMarkup('Leadership'),
    'teamwork' => new TranslatableMarkup('Teamwork'),
    'socializing' => new TranslatableMarkup('Socializing'),
    'influence' => new TranslatableMarkup('Influence'),
    'negotiation' => new TranslatableMarkup('Negotiation'),
    'collaboration' => new TranslatableMarkup('Collaboration'),
    'conflict_management' => new TranslatableMarkup('Conflict management'),
    'presentation_skills' => new TranslatableMarkup('Presentation skills'),
    'positive_outlook' => new TranslatableMarkup('Positive outlook'),
    'adaptability' => new TranslatableMarkup('Adaptability'),
    'self_control' => new TranslatableMarkup('Self-control'),
    'empathy' => new TranslatableMarkup('Empathy'),
    'innovation' => new TranslatableMarkup('Innovation'),
    'creativity' => new TranslatableMarkup('Creativity'),
    'future_planning' => new TranslatableMarkup('Future planning'),
    'time_management' => new TranslatableMarkup('Time management'),
    'goal_setting' => new TranslatableMarkup('Goal setting'),
    'problem_solving' => new TranslatableMarkup('Problem solving'),
    'decision_making' => new TranslatableMarkup('Decision making'),
    'analytical_skills' => new TranslatableMarkup('Analytical skills'),
    'finance_skills' => new TranslatableMarkup('Finance skills'),
    'growing_up' => new TranslatableMarkup('Growing up'),
    'mental_health' => new TranslatableMarkup('Mental health'),
    'physical_health' => new TranslatableMarkup('Physical health'),
    'healthy_eating' => new TranslatableMarkup('Healthy eating'),
    'useful_habits' => new TranslatableMarkup('Useful habits'),
    'digital_wellbeing' => new TranslatableMarkup('Digital wellbeing'),
    'culture_etiquette' => new TranslatableMarkup('Culture & Etiquette'),
    'harmony' => new TranslatableMarkup('Harmony'),
  ];
}

/**
 * Set dynamic allowed values for the interests field.
 *
 * @return array
 *   An array of possible key and value options.
 *
 * @see options_allowed_values()
 */
function bw_misc_vibe_allowed_values_function(): array {
  return [
    'pump_job' => new TranslatableMarkup('pump job/internship skills'),
    'discover_my_dream_job' => new TranslatableMarkup('discover my dream job'),
    'try_my_startup' => new TranslatableMarkup('try my startup'),
    'network_with_pros' => new TranslatableMarkup('network with pros'),
    'develop_life_skills' => new TranslatableMarkup('develop life skills'),
    'explore_careers' => new TranslatableMarkup('explore careers'),
    'join_collabs_projects' => new TranslatableMarkup('join collabs projects'),
    'build_my_portfolio' => new TranslatableMarkup('build my portfolio'),
    'just_looking_around' => new TranslatableMarkup('just looking around'),
  ];
}

/**
 * Implements hook_form_alter().
 */
function bw_misc_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  // Check if the form is the user edit form.
  if ('user_form' === $form_id) {
    $user = $form_state->getFormObject()->getEntity();
    $roles = ['student'];
    $fields_suffix = ['likes', 'follows', 'shares', 'views'];

    // Remove initial stats fields which are not for this user role.
    foreach ($roles as $role) {
      if (!in_array($role, $user->getRoles(), TRUE)) {
        foreach ($fields_suffix as $field_suffix) {
          $form['field_' . $role . '_initial_' . $field_suffix]['#access'] = FALSE;
        }
      }
    }
  }

  if ('media_bulk_upload_form' === $form_id) {
    $form['#submit'][] = '_bw_misc_media_bulk_submit';
  }

  if ($form_id == 'node_big5_edit_form') {
    _bw_misc_factor_value_field_add_attributes($form);
  }

  _bw_misc_number_field_remove_scrolling($form);
}

/**
 * Custom submit handler for the media bulk upload form.
 */
function _bw_misc_media_bulk_submit($form, &$form_state) {
  $form_state->setRedirect('entity.media.collection');
}

/**
 * Recursively searches for 'field_factor_value' and applies attributes.
 */
function _bw_misc_factor_value_field_add_attributes(array &$element) {
  foreach ($element as &$value) {
    // Check if the element is the desired number field.
    if (is_array($value) && isset($value['#field_name']) && $value['#field_name'] === 'field_factor_value' && isset($value[0]['value']['#type']) && $value[0]['value']['#type'] === 'number') {
      $value[0]['value']['#step'] = '0.5';
    }

    // Recurse into nested arrays to find other instances.
    if (is_array($value)) {
      _bw_misc_factor_value_field_add_attributes($value);
    }
  }
}

/**
 * Recursively searches for 'number' fields and removes browser scroll feature.
 */
function _bw_misc_number_field_remove_scrolling(array &$element) {
  foreach ($element as &$value) {
    // Check if the element is the desired number field.
    if (is_array($value) && isset($value['#type']) && $value['#type'] === 'number') {
      // Add attributes to prevent scroll.
      if (!isset($value['#attributes'])) {
        $value['#attributes'] = [];
      }
      $value['#attributes']['onwheel'] = 'event.preventDefault()';
    }

    // Recurse into nested arrays to find other instances.
    if (is_array($value)) {
      _bw_misc_number_field_remove_scrolling($value);
    }
  }
}

/**
 * Implements hook_ENTITY_TYPE_insert().
 *
 * On course or shortzzz publishing send notifications to users.
 */
function bw_misc_node_insert(EntityInterface $entity) {
  if (
    in_array(
      $entity->bundle(),
      _bw_misc_get_course_node_types(),
      TRUE,
    )
    && $entity->isPublished()
  ) {
    _bw_misc_send_notification_on_course_or_shorzzz_publishing($entity);
  }
  if ($entity->bundle() == 'big5' && $entity->isPublished()) {
    _bw_misc_send_notification_on_bigfive_publishing($entity);
  }
  if ($entity->bundle() == 'shortzzz' && $entity->isPublished()) {
    // _bw_misc_send_notification_on_shorzzz_publishing($entity);
  }
}

/**
 * Implements hook_ENTITY_TYPE_update().
 */
function bw_misc_node_update(EntityInterface $entity) {
  $feed_content = [
    'big5',
    'coming_soon',
    'alpha_gen',
    'shortzzz',
  ];

  $bundle = $entity->bundle();
  $is_published = $entity->isPublished();
  $was_published = $entity->original->isPublished();

  if (in_array($bundle, _bw_misc_get_course_node_types(), TRUE) && $is_published && !$was_published) {
    _bw_misc_send_notification_on_course_or_shorzzz_publishing($entity);
  }

  if ($bundle == 'big5' && $is_published && !$was_published) {
    _bw_misc_send_notification_on_bigfive_publishing($entity);
  }

  if ($bundle == 'shortzzz' && $is_published && !$was_published) {
    // _bw_misc_send_notification_on_shorzzz_publishing($entity);
  }

  if (in_array($bundle, $feed_content) && $was_published !== $is_published) {
    Cache::invalidateTags(['bw_user_feed_tag']);
  }

  if ($bundle === 'onboarding') {
    Cache::invalidateTags(['bw_user_onboarding_offers_user', 'bw_user_onboarding_user']);
  }
}

/**
 * Implements hook_ENTITY_TYPE_presave().
 */
function bw_misc_media_presave(EntityInterface $entity) {
  if ('audio' !== $entity->bundle()) {
    return;
  }

  $fileinfo = (new \getID3())->analyze(
    $entity->field_media_audio_file->entity->getFileUri()
  );
  $entity->set('field_media_audio_duration', $fileinfo['playtime_string']);

  Cache::invalidateTags(['bw_audio_files']);
}

/**
 * Implements hook_ENTITY_TYPE_presave().
 */
function bw_misc_profile_presave(EntityInterface $entity) {
  // NOTE: Disabled based on https://brayn.atlassian.net/browse/BW-2593.
  if (FALSE) {
    $notification = \Drupal::service('bw_notification');

    if ('student' === $entity->bundle()) {
      $user_brayn_bits = 0;
      foreach ($entity->field_s_skills->referencedEntities() as $profile_skill) {
        $user_brayn_bits += (int) $profile_skill->field_ss_braynbits->value;
      }

      $levels = [
        1 => 150,
        2 => 300,
        3 => 450,
        4 => 680,
        5 => 950,
        6 => 1200,
        7 => 1800,
        8 => 2700,
        9 => 4050,
        10 => 6075,
        11 => 9113,
        12 => 13669,
        13 => 20503,
        14 => 30755,
        15 => 46132,
        16 => 69198,
        17 => 103797,
        18 => 155696,
        19 => 233543,
      ];

      $rewards = [
        1 => ['neurons' => 5],
        2 => ['neurons' => 5],
        3 => ['neurons' => 5],
        4 => ['neurons' => 5],
        5 => ['neurons' => 5],
        6 => ['neurons' => 5],
        7 => ['neurons' => 5],
        8 => ['neurons' => 5],
        9 => ['neurons' => 5],
        10 => [
          'neurons' => 5,
          'axons' => 1,
        ],
        11 => ['neurons' => 10],
        12 => ['neurons' => 15],
        13 => ['neurons' => 20],
        14 => ['neurons' => 25],
        15 => ['neurons' => 30],
        16 => ['neurons' => 35],
        17 => ['neurons' => 40],
        18 => ['neurons' => 45],
        19 => ['neurons' => 50],
      ];

      $user_old_level = (int) ($entity->field_s_level->value ?? '0');
      $user_reached_level = (int) ($entity->field_s_reached_level->value ?? '0');
      $user_new_level = 0;

      if (0 === $user_brayn_bits) {
        $entity->set('field_s_level', 0);
        return;
      }

      foreach ($levels as $level => $max_brayn_bits) {
        if ($user_brayn_bits >= $max_brayn_bits) {
          $user_new_level = $level;
        }
      }

      if ($user_old_level === $user_new_level) {
        return;
      }

      $entity->set('field_s_level', $user_new_level);

      if ($user_new_level > $user_reached_level) {
        $entity->set('field_s_reached_level', $user_new_level);

        $entity->set(
        'field_s_neurons',
        ($entity->field_s_neurons->value ?? 0) + $rewards[$user_new_level]['neurons'],
        );

        if (isset($rewards[$user_new_level]['axons'])) {
          $entity->set(
            'field_s_axons',
            ($entity->field_s_axons->value ?? 0) + $rewards[$user_new_level]['axons']
          );
        }

        $notification->send(
          $entity,
          [
            'contents' => [
              'en' => new TranslatableMarkup("🚀 You’ve leveled up! Stay awesome—your dream job awaits!"),
            ],
            'include_external_user_ids' => [
              $entity->uid->entity->uuid(),
            ],
            'data' => [
              'type' => 'level_rewards',
              'level' => $user_new_level,
              'level_rewards' => $rewards[$user_new_level],
            ],
          ],
          FALSE,
        );
        \Drupal::queue('bw_trophies_queue')->createItem([
          'id' => $entity->uid->entity->id(),
          'types' => ['achiever'],
        ]);
      }
    }
  }
}

/**
 * Implements hook_ENTITY_TYPE_delete().
 *
 * Clear the course cache when deleting a hashtag entity.
 */
function bw_misc_taxonomy_term_delete(EntityInterface $entity) {
  bw_misc_taxonomy_term_update($entity);
}

/**
 * Implements hook_ENTITY_TYPE_update().
 *
 * Clear the course cache when updating a hashtag entity.
 */
function bw_misc_taxonomy_term_update(EntityInterface $entity) {
  if ('hashtag' !== $entity->bundle()) {
    return;
  }

  $course_ids = \Drupal::entityTypeManager()
    ->getStorage('node')
    ->getQuery()
    ->accessCheck(FALSE)
    ->condition('field_cs1_hashtags', $entity->id())
    ->condition('type', _bw_misc_get_course_node_types(), 'IN')
    ->execute();
  foreach ($course_ids as $course_id) {
    Cache::invalidateTags(["node:{$course_id}"]);
  }

  $shortzzz_ids = \Drupal::entityTypeManager()
    ->getStorage('node')
    ->getQuery()
    ->accessCheck(FALSE)
    ->condition('field_sz_hashtags', $entity->id())
    ->condition('type', 'shortzzz')
    ->execute();
  foreach ($shortzzz_ids as $shortzzz_id) {
    Cache::invalidateTags(["node:{$shortzzz_id}"]);
  }
}

/**
 * Set dynamic allowed values for the skills field.
 *
 * @return array
 *   An array of possible key and value options.
 *
 * @see options_allowed_values()
 */
function bw_misc_skills_allowed_values_function(): array {
  return [
    'lifestyle_skills' => new TranslatableMarkup('Lifestyle Skills'),
    'impactful_communication' => new TranslatableMarkup('Impactful Communication'),
    'resilience_and_wellbeing' => new TranslatableMarkup('Resilience and Well-Being'),
    'entrepreneurship_and_innovation' => new TranslatableMarkup('Entrepreneurship & Innovation'),
    'cognitive_evolution' => new TranslatableMarkup('Cognitive Evolution'),
    'leadership' => new TranslatableMarkup('Leadership'),
  ];
}

/**
 * Return all the course node types.
 *
 * @return array
 *   The course node types.
 */
function _bw_misc_get_course_node_types(): array {
  return ['course_structure_1', 'course_structure_2', 'course_structure_3'];
}

/**
 * Return the skill's interests.
 *
 * @return array
 *   The skill's interests.
 */
function _bw_misc_get_skills_interests(): array {
  return [
    'self_concept' => ['self_concept', 'self_control'],
    'emotional_intelligence' => [
      'emotional_intelligence',
      'empathy',
      'influence',
      'conflict_management',
      'teamwork',
      'collaboration',
      'self_control',
      'adaptability',
    ],
    'resilience_and_stress_management' => [
      'resilience_and_stress_management',
      'future_planning',
      'adaptability',
      'critical_thinking',
      'strategic_thinking',
      'time_management',
      'communication',
      'teamwork',
      'self_concept',
      'goal_setting',
      'problem_solving',
    ],
    'self_organization' => [
      'self_organization',
      'time_management',
      'goal_setting',
      'decision_making',
      'problem_solving',
    ],
    'strategic_thinking' => [
      'strategic_thinking',
      'innovation',
      'problem_solving',
      'communication',
      'leadership',
      'analytical_skills',
    ],
    'critical_thinking' => [
      'critical_thinking',
      'problem_solving',
      'decision_making',
      'analytical_skills',
    ],
    'systemic_thinking' => [
      'systemic_thinking',
      'creativity',
      'analytical_skills',
    ],
    'lifestyle_thinking' => [
      'lifestyle_thinking',
      'physical_health',
      'healthy_eating',
      'finance_skills',
      'useful_habits',
      'digital_wellbeing',
      'mental_health',
      'growing_up',
      'culture_etiquette',
      'harmony',
      'time_management',
      'critical_thinking',
      'emotional_intelligence',
      'conflict_management',
      'leadership',
      'communication',
      'presentation_skills',
      'goal_setting',
      'problem_solving',
    ],
    'design_thinking' => [
      'design_thinking',
      'empathy',
      'problem_solving',
      'critical_thinking',
      'creativity',
      'systemic_thinking',
      'leadership',
      'collaboration',
      'communication',
      'innovation',
    ],
    'communication' => [
      'communication',
      'negotiation',
      'presentation_skills',
      'conflict_management',
      'empathy',
      'socializing',
    ],
    'leadership' => [
      'leadership',
      'adaptability',
      'positive_outlook',
      'empathy',
      'influence',
      'conflict_management',
      'teamwork',
      'self_control',
    ],
    'teamwork' => [
      'teamwork',
      'communication',
      'problem_solving',
      'critical_thinking',
      'collaboration',
      'leadership',
      'time_management',
    ],
  ];
}

/**
 * Send notification to all user on publishing of a course or shortzzz.
 *
 * @param \Drupal\Core\Entity\EntityInterface $entity
 *   The course or shortzzz entity.
 */
function _bw_misc_send_notification_on_course_or_shorzzz_publishing(EntityInterface $entity): void {
  \Drupal::service('bw_notification')->sendToAll(
    [
      'contents' => ['en' => new TranslatableMarkup("���� New module available! Boost your skills and get closer to your dream job 🤟")],
      'filters' => [
        [
          'field' => 'tag',
          'key' => 'environment',
          'relation' => '=',
          'value' => Settings::get('app.environment'),
        ],
      ],
      'data' => array_filter([
        'type' => 'new_course_or_shortzzz',
        'shortzzz_project_id' => 'shortzzz' === $entity->bundle()
          ? $entity->id()
          : NULL,
      ]),
    ]
  );
}

/**
 * Send notification to all user on publishing of a course or shortzzz.
 *
 * @param \Drupal\Core\Entity\EntityInterface $entity
 *   The course or shortzzz entity.
 */
function _bw_misc_send_notification_on_shorzzz_publishing(EntityInterface $entity): void {
  \Drupal::service('bw_notification')->sendToAll(
    [
      'contents' => ['en' => new TranslatableMarkup("🚀 Check out the new nano-boost. Available now!")],
      'filters' => [
        [
          'field' => 'tag',
          'key' => 'environment',
          'relation' => '=',
          'value' => Settings::get('app.environment'),
        ],
      ],
      'data' => array_filter([
        'type' => 'new_shortzzz',
        'shortzzz_id' => 'shortzzz' === $entity->bundle()
          ? $entity->id()
          : NULL,
      ]),
    ]
  );
}

/**
 * Send notification to all user on publishing of a course or shortzzz.
 *
 * @param \Drupal\Core\Entity\EntityInterface $entity
 *   The course or shortzzz entity.
 */
function _bw_misc_send_notification_on_bigfive_publishing(EntityInterface $entity): void {

  \Drupal::service('bw_notification')->sendToAll(
    [
      'contents' => ['en' => new TranslatableMarkup("Discover your potential with BRAYN Scan. Check out this new feature!")],
      'filters' => [
        [
          'field' => 'tag',
          'key' => 'environment',
          'relation' => '=',
          'value' => Settings::get('app.environment'),
        ],
      ],
      'data' => array_filter([
        'type' => 'new_big5',
        'big5_id' => $entity->id(),
      ]),
    ]
  );
}

/**
 * Implements hook_entity_access().
 */
function bw_misc_entity_access(EntityInterface $entity, $operation, AccountInterface $account) {
  if ($entity->getEntityTypeId() === 'shortzzz_project') {
    $roles = $account->getRoles();
    if (in_array('administrator', $roles) || in_array('super_administrator', $roles)) {
      return AccessResult::allowed();
    }
  }
  return AccessResult::neutral();
}

/**
 * Add certification to student profile if they don't already have it.
 *
 * @param \Drupal\profile\Entity\Profile $student_profile
 *   The student profile entity.
 * @param int $course_id
 *   The course ID to create certification for.
 */
function _bw_misc_add_certification($student_profile, int $course_id) {
  if (!$student_profile->hasField('field_certifications')) {
    return;
  }

  $certifications = $student_profile->field_certifications->referencedEntities();
  $has_certification = array_reduce($certifications, function ($carry, $certification) use ($course_id) {
    return $carry || ($certification->field_course->target_id == $course_id);
  }, FALSE);

  if (!$has_certification) {
    $paragraph = Paragraph::create([
      'type' => 'certifications',
      'field_course' => ['target_id' => $course_id],
      'field_date' => [
        'value' => (new \DateTime())->format('Y-m-d\TH:i:s'),
      ],
    ]);
    $paragraph->save();

    $student_profile->field_certifications[] = [
      'target_id' => $paragraph->id(),
      'target_revision_id' => $paragraph->getRevisionId(),
    ];
    $student_profile->save();

    // @todo Sent push notification to the user for the certification.
    \Drupal::service('bw_notification')->send(
      $student_profile,
      [
        'contents' => ['en' => new TranslatableMarkup("Congrats! You’ve unlocked a skill-proof token!")],
        'include_external_user_ids' => [$student_profile->uid->entity->uuid()],
        'data' => [
          'type' => 'certification_received',
          'course_id' => $course_id,
        ],
      ]
    );
  }
}

/**
 * Implements hook_entity_view_alter().
 */
function bw_misc_entity_view_alter(array &$build, EntityInterface $entity, $view_mode) {
  if (!$entity instanceof ParagraphInterface) {
    return;
  }

  // Get the parent entity.
  $parent = $entity->getParentEntity();
  if (!$parent) {
    return;
  }

  // For Trophy entities, reward paragraphs are in rewards_id.
  if ($parent->getEntityTypeId() === 'trophy' && $entity->bundle() === 'reward') {
    $hidden_fields = ['field_trophy', 'field_skills'];
  }
  else {
    return;
  }

  // Hide the specified fields.
  foreach ($hidden_fields as $hidden_field) {
    if (isset($build[$hidden_field])) {
      $build[$hidden_field]['#access'] = FALSE;
    }
  }
}

/**
 * Implements hook_field_widget_single_element_form_alter().
 */
function bw_misc_field_widget_single_element_form_alter(
  array &$element,
  FormStateInterface $form_state,
) {
  if (empty($element['#paragraph_type'])) {
    return;
  }

  // Get the parent entity and field name.
  if (!($parent = _bw_misc_get_from_state_parent_entity($form_state))) {
    return;
  }

  // For Trophy entities, reward paragraphs are in rewards_id.
  if ($parent->getEntityTypeId() === 'trophy' && $element['#paragraph_type'] === 'reward') {
    $hidden_fields = ['field_trophy', 'field_skills'];
  }
  else {
    return;
  }

  // Hide the specified fields.
  foreach ($hidden_fields as $hidden_field) {
    if (isset($element['subform'][$hidden_field])) {
      $element['subform'][$hidden_field]['#access'] = FALSE;
    }
  }
}

/**
 * Helper function to get the parent entity from the form state.
 */
function _bw_misc_get_from_state_parent_entity(FormStateInterface $form_state) {
  $form_object = $form_state->getFormObject();
  if (method_exists($form_object, 'getEntity')) {
    return $form_object->getEntity();
  }
  return NULL;
}

/**
 * Implements hook_entity_update().
 */
function bw_misc_entity_update(EntityInterface $entity) {
  if (\Drupal::state()->get('bw_misc.debug_entity_updates', FALSE)) {
    $e = (new Exception());
    // Extract second item from trace.
    $trace_caller = _bw_misc_entity_debug_get_trace_caller($e);
    // Log when an entity is updated including stack trace for debugging.
    \Drupal::logger('entity_debug')->debug('Entity @entity_type @entity_id updated. @stacktrace', [
      '@entity_type' => $entity->getEntityTypeId(),
      '@entity_id' => $entity->id(),
      '@stacktrace' => print_r($trace_caller, TRUE),
    ]);
  }
}

/**
 * Implements hook_entity_delete().
 */
function bw_misc_entity_delete(EntityInterface $entity) {
  if (\Drupal::state()->get('bw_misc.debug_entity_updates', FALSE)) {
    $e = (new Exception());
    $trace = _bw_misc_entity_debug_get_trace_caller($e);
    // Extract second item from trace.
    $trace_caller = array_slice($trace, 1, 1);

    // Log when an entity is deleted including stack trace for debugging.
    \Drupal::logger('entity_debug')->debug('Entity @entity_type @entity_id deleted. @stacktrace', [
      '@entity_type' => $entity->getEntityTypeId(),
      '@entity_id' => $entity->id(),
      '@stacktrace' => print_r($trace_caller, TRUE),
    ]);
  }
}

/**
 * Limit trace to the first call to entity save.
 *
 * @return mixed
 *   Return the trace information.
 */
function _bw_misc_entity_debug_get_trace_caller(\Exception $exception) : array {
  $trace = $exception->getTrace();
  if (count($trace) === 0) {
    return [];
  }
  if (count($trace) === 1) {
    return $trace;
  }
  do {
    $line = array_shift($trace);
    // Get class name from trace.
    $class = $line['class'] ?? NULL;
    $function = $line['function'] ?? NULL;
  } while (!isset($class) || (!($class instanceof EntityStorageInterface) && ($function !== 'save')));
  return array_slice($trace, 1, 1);
}
