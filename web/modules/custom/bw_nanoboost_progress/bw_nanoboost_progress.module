<?php

/**
 * @file
 * Module-related hooks.
 */

use Drupal\Core\Database\Query\SelectInterface;

/**
 * Implements hook_query_TAG_alter().
 *
 * This hook alters entity queries tagged with 'module_type_filter' to filter
 * entities that reference shortzzz nodes of specific type.
 */
function bw_nanoboost_progress_query_entity_query_alter(SelectInterface $query) {
  if ($query->hasTag('shortzzz_type_filter')) {
    // Get metadata from the query.
    $query_field_name = $query->getMetaData('query_field_name');
    $filter_sz_types = $query->getMetaData('filter_sz_types');

    if ($query_field_name && $filter_sz_types) {
      // Get the node IDs that have the specified type.
      $entity_storage = \Drupal::entityTypeManager()->getStorage('node');
      $node_ids = $entity_storage->getQuery()
        ->accessCheck(FALSE)
        ->condition('type', 'shortzzz')
        ->condition('field_sz_type', $filter_sz_types, 'IN')
        ->execute();

      if (!empty($node_ids)) {
        // Add a condition to filter by the filtered node IDs.
        $query->condition($query_field_name, $node_ids, 'IN');
      }
      else {
        // If no nodes of the specified type exist, ensure no results by adding
        // an impossible condition.
        $query->condition($query_field_name, 0);
      }
    }
  }
}
