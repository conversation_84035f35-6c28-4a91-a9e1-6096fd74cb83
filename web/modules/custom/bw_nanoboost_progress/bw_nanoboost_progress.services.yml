services:
  bw_nanoboost_progress.nano_framework_reward_service:
    class: Drupal\bw_nanoboost_progress\NanoFrameworkRewardService
    arguments: ['@entity_type.manager']
  bw_nanoboost_progress.shortzzz_reward_service:
    class: Drupal\bw_nanoboost_progress\ShortzzzRewardService
    arguments: ['@entity_type.manager', '@database', '@queue']
  bw_nanoboost_progress.nanoboost_data_service:
    class: Drupal\bw_nanoboost_progress\NanoboostDataService
    arguments: ['@database', '@queue']
