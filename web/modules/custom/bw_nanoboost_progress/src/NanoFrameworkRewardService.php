<?php

namespace <PERSON><PERSON>al\bw_nanoboost_progress;

use <PERSON><PERSON><PERSON>\bw_token\Entity\Token;
use <PERSON><PERSON>al\bw_trophies\Entity\Trophy;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\node\NodeInterface;
use <PERSON><PERSON><PERSON>\profile\Entity\Profile;

/**
 * Service for handling nano-framework-related rewards.
 */
final class NanoFrameworkRewardService {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected EntityTypeManagerInterface $entityTypeManager;

  /**
   * Constructs a new NanoFrameworkRewardService.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   */
  public function __construct(EntityTypeManagerInterface $entity_type_manager) {
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * Awards rewards for a completed nano-framework.
   *
   * @param \Drupal\node\NodeInterface $nano_framework_node
   *   The nano-framework node containing reward information.
   * @param int $user_id
   *   The user ID for trophy/token awards.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  public function awardNanoFrameworkCompletionRewards(NodeInterface $nano_framework_node, int $user_id): void {
    $student_profile = $this->entityTypeManager->getStorage('profile')
      ->loadByProperties([
        'type' => 'student',
        'uid' => $user_id,
      ]);
    $student_profile = reset($student_profile);

    if (!$student_profile) {
      return;
    }

    $save_profile = FALSE;
    $save_profile = $this->awardSkills($nano_framework_node, $student_profile) || $save_profile;
    $save_profile = $this->awardRewards($nano_framework_node, $student_profile) || $save_profile;

    // Save profile if any changes were made.
    if ($save_profile) {
      $student_profile->save();
    }

    $this->awardTrophy($nano_framework_node, $user_id);
    $this->awardToken($nano_framework_node, $user_id);
  }

  /**
   * Awards skills and braynbits from a nano-framework node.
   *
   * @param \Drupal\node\NodeInterface $nano_framework_node
   *   The nano-framework node containing skills information.
   * @param \Drupal\profile\Entity\Profile $student_profile
   *   The student profile to update.
   *
   * @return bool
   *   TRUE if the profile was modified, FALSE otherwise.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function awardSkills(NodeInterface $nano_framework_node, Profile $student_profile): bool {
    $skills = $nano_framework_node->field_sz_skills->referencedEntities();
    if (empty($skills)) {
      return FALSE;
    }

    $profile_skills = $student_profile->field_s_skills->referencedEntities();
    $profile_skills_map = [];
    foreach ($profile_skills as $profile_skill) {
      $profile_skills_map[$profile_skill->field_ss_skill->value] = $profile_skill;
    }

    $paragraph_storage = $this->entityTypeManager->getStorage('paragraph');
    $profile_modified = FALSE;

    foreach ($skills as $skill) {
      $skill_name = $skill->field_sc_skill->value;
      $brayn_bits = (int) $skill->field_sc_braynbits->value;

      if (isset($profile_skills_map[$skill_name])) {
        // Update existing skill.
        $profile_skill = $profile_skills_map[$skill_name];
        $new_value = $brayn_bits + (int) $profile_skill->field_ss_braynbits->value;
        $profile_skill->set('field_ss_braynbits', $new_value);
      }
      else {
        // Create new skill.
        $profile_skill = $paragraph_storage->create([
          'type' => 'skill_student',
          'field_ss_braynbits' => $brayn_bits,
          'field_ss_skill' => $skill_name,
        ]);
        $student_profile->field_s_skills[] = $profile_skill;
      }

      $profile_skill->save();
      $profile_modified = TRUE;
    }

    return $profile_modified;
  }

  /**
   * Awards rewards (neurons, axons) from a nano-framework node.
   *
   * @param \Drupal\node\NodeInterface $nano_framework_node
   *   The nano-framework node containing rewards information.
   * @param \Drupal\profile\Entity\Profile $student_profile
   *   The student profile to update.
   *
   * @return bool
   *   TRUE if the profile was modified, FALSE otherwise.
   */
  protected function awardRewards(NodeInterface $nano_framework_node, Profile $student_profile): bool {
    $reward_fields = [
      'field_rewards_neurons' => 'field_s_neurons',
      'field_rewards_axon' => 'field_s_axons',
    ];

    $profile_modified = FALSE;

    foreach ($reward_fields as $node_field => $profile_field) {
      $reward = $nano_framework_node->get($node_field)->value ?? 0;
      if ($reward > 0) {
        $current_value = $student_profile->get($profile_field)->value ?? 0;
        $student_profile->set($profile_field, $current_value + $reward);
        $profile_modified = TRUE;
      }
    }

    return $profile_modified;
  }

  /**
   * Awards a trophy if it exists and is not already awarded.
   *
   * @param \Drupal\node\NodeInterface $nano_framework_node
   *   The nano-framework node containing trophy information.
   * @param int $user_id
   *   The user ID to award the trophy to.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function awardTrophy(NodeInterface $nano_framework_node, int $user_id): void {
    if (!($trophy = $nano_framework_node->field_sz_trophy->entity) instanceof Trophy) {
      return;
    }

    $user_trophy_storage = $this->entityTypeManager->getStorage('user_trophy');
    $user_trophies = $user_trophy_storage->loadByProperties([
      'uid' => $user_id,
      'trophy' => $trophy->id(),
    ]);

    if (empty($user_trophies)) {
      $user_trophy_storage->create([
        'uid' => $user_id,
        'trophy' => $trophy->id(),
        'date_acquired' => (new \DateTime())->getTimestamp(),
      ])->save();
    }
  }

  /**
   * Awards a token if it exists and is not already awarded.
   *
   * @param \Drupal\node\NodeInterface $nano_framework_node
   *   The nano-framework node containing token information.
   * @param int $user_id
   *   The user ID to award the token to.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function awardToken(NodeInterface $nano_framework_node, int $user_id): void {
    if (!($token = $nano_framework_node->field_sz_token->entity) instanceof Token) {
      return;
    }

    $user_token_storage = $this->entityTypeManager->getStorage('user_token');
    $user_tokens = $user_token_storage->loadByProperties([
      'uid' => $user_id,
      'token' => $token->id(),
    ]);

    if (empty($user_tokens)) {
      $user_token_storage->create([
        'uid' => $user_id,
        'token' => $token->id(),
        'date_acquired' => (new \DateTime())->getTimestamp(),
      ])->save();
    }
  }

}
