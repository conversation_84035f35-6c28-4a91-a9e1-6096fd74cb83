<?php

namespace Drupal\bw_nanoboost_progress;

use <PERSON><PERSON><PERSON>\bw_trophies\Entity\Trophy;
use <PERSON><PERSON>al\Core\Database\Connection;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON><PERSON>\Core\Queue\QueueFactory;
use <PERSON><PERSON><PERSON>\profile\Entity\Profile;

/**
 * Service for handling nanoboost data updates to user profiles.
 */
final class NanoboostDataService {

  /**
   * The database connection.
   *
   * @var \Drupal\Core\Database\Connection
   */
  private Connection $connection;

  /**
   * The queue factory.
   *
   * @var \Drupal\Core\Queue\QueueFactory
   */
  private QueueFactory $queueFactory;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected EntityTypeManagerInterface $entityTypeManager;

  /**
   * Constructs a new NanoboostDataService.
   *
   * @param \Drupal\Core\Database\Connection $connection
   *   The database connection.
   * @param \Drupal\Core\Queue\QueueFactory $queue_factory
   *   The queue factory.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   */
  public function __construct(Connection $connection, QueueFactory $queue_factory, EntityTypeManagerInterface $entity_type_manager) {
    $this->connection = $connection;
    $this->queueFactory = $queue_factory;
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * Updates user profile with nano-framework data.
   *
   * @param array $progress_data
   *   The nano-framework progress data.
   * @param int $user_id
   *   The user ID.
   * @param \Drupal\profile\Entity\Profile|null $student_profile
   *   The student profile (optional, otherwise loaded by user ID).
   * @param bool $auto_save
   *   Whether to automatically save the profile (default: false).
   *
   * @return bool
   *   TRUE if the profile was updated, FALSE otherwise.
   */
  public function updateUserNanoboostData(
    array $progress_data,
    int $user_id,
    ?Profile $student_profile = NULL,
    bool $auto_save = FALSE,
  ): bool {
    // Load profile if not provided.
    if (!$student_profile) {
      $student_profile = $this->entityTypeManager->getStorage('profile')
        ->loadByProperties([
          'type' => 'student',
          'uid' => $user_id,
        ]);
      /** @var \Drupal\profile\Entity\Profile $student_profile */
      $student_profile = reset($student_profile);

      if (!$student_profile) {
        return FALSE;
      }
    }

    $updated = FALSE;
    foreach ($progress_data as $item) {
      $field_name = 'field_' . $item['type'];
      if ($student_profile->hasField($field_name)) {
        $field_data = $student_profile->get($field_name)->getValue();
        $current_value = !empty($field_data[0]['value']) ? unserialize($field_data[0]['value'], ['allowed_classes' => FALSE]) : [];

        if (!isset($current_value['value']) || $current_value['value'] !== $item['value']) {
          $current_value['value'] = $item['value'];
          if (!empty($item['id'])) {
            $current_value['id'] = $item['id'];
          }

          $student_profile->set($field_name, serialize($current_value));
          $updated = TRUE;

          // Handle radar field trophy logic.
          if ($field_name === 'field_radar') {
            $this->handleRadarTrophy($user_id);
          }
        }
      }
    }

    if ($updated && $auto_save) {
      $student_profile->save();
    }

    return $updated;
  }

  /**
   * Handles the BALANCE_MASTER trophy for radar field updates.
   *
   * @param int $user_id
   *   The user ID.
   */
  private function handleRadarTrophy(int $user_id): void {
    // Check if user already has the BALANCE_MASTER trophy.
    $has_balance_master_trophy = (bool) $this->connection
      ->select('bw_user_trophy', 'ut')
      ->fields('ut', ['trophy'])
      ->condition('uid', $user_id)
      ->condition('trophy', Trophy::TROPHY_ACTIVITY_BALANCE_MASTER . "_junior")
      ->countQuery()
      ->execute()
      ->fetchField();

    if (!$has_balance_master_trophy) {
      $this->queueFactory->get('bw_trophies_queue')->createItem([
        'id' => $user_id,
        'types' => [Trophy::TROPHY_ACTIVITY_BALANCE_MASTER],
      ]);
    }
  }

}
