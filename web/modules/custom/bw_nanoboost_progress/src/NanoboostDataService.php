<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>\bw_nanoboost_progress;

use <PERSON><PERSON><PERSON>\bw_trophies\Entity\Trophy;
use <PERSON><PERSON>al\Core\Database\Connection;
use <PERSON><PERSON>al\Core\Queue\QueueFactory;
use <PERSON><PERSON><PERSON>\node\NodeInterface;
use <PERSON><PERSON><PERSON>\profile\Entity\Profile;

/**
 * Service for handling nanoboost data updates to user profiles.
 */
class NanoboostDataService {

  /**
   * The database connection.
   *
   * @var \Drupal\Core\Database\Connection
   */
  private Connection $connection;

  /**
   * The queue factory.
   *
   * @var \Drupal\Core\Queue\QueueFactory
   */
  private QueueFactory $queueFactory;

  /**
   * Constructs a new NanoboostDataService.
   *
   * @param \Drupal\Core\Database\Connection $connection
   *   The database connection.
   * @param \Drupal\Core\Queue\QueueFactory $queue_factory
   *   The queue factory.
   */
  public function __construct(Connection $connection, QueueFactory $queue_factory) {
    $this->connection = $connection;
    $this->queueFactory = $queue_factory;
  }

  /**
   * Updates user profile with nanoboost data.
   *
   * @param \Drupal\profile\Entity\Profile $student_profile
   *   The student profile.
   * @param array $progress_data
   *   The nanoboost progress data.
   * @param int $user_id
   *   The user ID.
   * @param \Drupal\node\NodeInterface|null $node
   *   The nanoboost node (optional, for storing node ID with data).
   * @param array|null $allowed_types
   *   Array of allowed types to process (optional, processes all if null).
   * @param bool $auto_save
   *   Whether to automatically save the profile (default: false).
   *
   * @return bool
   *   TRUE if the profile was updated, FALSE otherwise.
   */
  public function updateNanoboostData(
    Profile $student_profile,
    array $progress_data,
    int $user_id,
    ?NodeInterface $node = NULL,
    ?array $allowed_types = NULL,
    bool $auto_save = FALSE
  ): bool {
    $updated = FALSE;

    foreach ($progress_data as $item) {
      $field_name = 'field_' . $item['type'];

      // Filter by allowed types if specified.
      if ($allowed_types !== NULL && !in_array($item['type'], $allowed_types)) {
        continue;
      }

      if ($student_profile->hasField($field_name)) {
        $field_data = $student_profile->get($field_name)->getValue();
        $current_value = !empty($field_data[0]['value']) ? unserialize($field_data[0]['value'], ['allowed_classes' => FALSE]) : [];

        $should_update = FALSE;
        $new_value = [];

        if ($node !== NULL) {
          // NanoBoostProgressResource pattern: store with node ID.
          if (isset($current_value['id']) && $current_value['id'] == $node->id()) {
            $new_value = [
              'id' => $node->id(),
              'value' => $item['value'],
            ];
            $should_update = TRUE;
          }
          elseif (!isset($current_value['id'])) {
            $new_value = [
              'id' => $node->id(),
              'value' => $item['value'],
            ];
            $should_update = TRUE;
          }
        }
        else {
          // UserUpdateResource pattern: store only value.
          if (!isset($current_value['value']) || $current_value['value'] !== $item['value']) {
            $new_value = $current_value;
            $new_value['value'] = $item['value'];
            $should_update = TRUE;
          }
        }

        if ($should_update) {
          $student_profile->set($field_name, serialize($new_value));
          $updated = TRUE;

          // Handle radar field trophy logic.
          if ($field_name === 'field_radar') {
            $this->handleRadarTrophy($user_id);
          }
        }
      }
    }

    if ($auto_save && $updated) {
      $student_profile->save();
    }

    return $updated;
  }

  /**
   * Handles the BALANCE_MASTER trophy for radar field updates.
   *
   * @param int $user_id
   *   The user ID.
   */
  private function handleRadarTrophy(int $user_id): void {
    // Check if user already has the BALANCE_MASTER trophy.
    $has_balance_master_trophy = (bool) $this->connection
      ->select('bw_user_trophy', 'ut')
      ->fields('ut', ['trophy'])
      ->condition('uid', $user_id)
      ->condition('trophy', Trophy::TROPHY_ACTIVITY_BALANCE_MASTER . "_junior")
      ->countQuery()
      ->execute()
      ->fetchField();

    if (!$has_balance_master_trophy) {
      $this->queueFactory->get('bw_trophies_queue')->createItem([
        'id' => $user_id,
        'types' => [Trophy::TROPHY_ACTIVITY_BALANCE_MASTER],
      ]);
    }
  }

}
