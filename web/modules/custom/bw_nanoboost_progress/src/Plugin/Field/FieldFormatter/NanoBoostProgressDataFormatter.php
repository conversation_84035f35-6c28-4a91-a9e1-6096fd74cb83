<?php

namespace <PERSON><PERSON>al\bw_nanoboost_progress\Plugin\Field\FieldFormatter;

use <PERSON><PERSON>al\Core\Field\FieldItemListInterface;
use <PERSON><PERSON>al\Core\Field\FormatterBase;

/**
 * Plugin implementation of the 'nanoboost_progress_data_formatter' formatter.
 *
 * @FieldFormatter(
 *   id = "nanoboost_progress_data_formatter",
 *   label = @Translation("NanoBoost Progress Data Formatter"),
 *   field_types = {
 *     "map"
 *   }
 * )
 */
class NanoBoostProgressDataFormatter extends FormatterBase {

  /**
   * {@inheritdoc}
   */
  public function viewElements(FieldItemListInterface $items, $langcode) {
    $elements = [];

    foreach ($items as $delta => $item) {
      $value = $item->getValue();
      if (empty($value)) {
        continue;
      }

      $data = $this->getDataFromValue($value);
      if (empty($data)) {
        continue;
      }

      $elements[$delta] = $this->buildContainer($data);
    }

    return $elements;
  }

  /**
   * Gets the data from the field value.
   *
   * @param mixed $value
   *   The field value.
   *
   * @return array
   *   The processed data array.
   */
  protected function getDataFromValue($value) {
    if (is_string($value)) {
      $value = unserialize($value, ['allowed_classes' => FALSE]);
    }

    // Handle new component-based structure.
    if (is_array($value)) {
      // Check if this is the new component-based structure.
      if ($this->isComponentBasedStructure($value)) {
        return $this->extractDataFromComponents($value);
      }

      // Handle legacy structure.
      if (isset($value['value'])) {
        return $value['value'];
      }
    }

    return [];
  }

  /**
   * Builds the container element with all data tables.
   *
   * @param array $data
   *   The data array to process.
   *
   * @return array
   *   The render array for the container.
   */
  protected function buildContainer(array $data) {
    $container = [
      '#type' => 'container',
      '#attributes' => [
        'class' => ['nanoboost-progress-data'],
        'style' => 'max-height: 350px; overflow-y: scroll;',
      ],
    ];

    // Collect all data items from either position-based or flat structure.
    $all_data_items = $this->collectAllDataItems($data);

    // Create a single table with all data items.
    if (!empty($all_data_items)) {
      $container[] = $this->createSingleTable($all_data_items);
    }

    return $container;
  }

  /**
   * Collects all data items from either position-based or flat structure.
   *
   * @param array $data
   *   The original data array.
   *
   * @return array
   *   Flattened array of all data items.
   */
  protected function collectAllDataItems(array $data) {
    $all_data_items = [];
    $has_positions = $this->hasPositionStructure($data);

    if ($has_positions) {
      foreach ($data as $position_data) {
        if (!is_array($position_data) || empty($position_data)) {
          continue;
        }

        foreach ($position_data as $data_item) {
          if (isset($data_item['type']) && isset($data_item['value'])) {
            $all_data_items[] = $data_item;
          }
        }
      }
    }
    else {
      foreach ($data as $data_item) {
        if (isset($data_item['type']) && isset($data_item['value'])) {
          $all_data_items[] = $data_item;
        }
      }
    }

    return $all_data_items;
  }

  /**
   * Creates a single table for all data items.
   *
   * @param array $data_items
   *   All data items to display.
   *
   * @return array
   *   The render array for the table.
   */
  protected function createSingleTable(array $data_items) {
    $table_rows = [];
    $column_keys = [];

    // Process all items to determine all possible column keys.
    foreach ($data_items as $data_item) {
      $value = $data_item['value'];

      if (is_array($value)) {
        foreach ($value as $array_item) {
          if (is_array($array_item)) {
            foreach ($array_item as $key => $val) {
              if (!in_array($key, $column_keys)) {
                $column_keys[] = $key;
              }
            }
          }
        }
      }
    }

    // Now create rows for all items.
    foreach ($data_items as $data_item) {
      $type = $data_item['type'];
      $value = $data_item['value'];

      if (is_array($value)) {
        foreach ($value as $array_item) {
          if (is_array($array_item)) {
            $row = [];
            $row[] = ['data' => $type, 'header' => TRUE];

            // Fill in values for each column.
            foreach ($column_keys as $key) {
              $cell_value = isset($array_item[$key]) ? $this->formatValue($array_item[$key]) : '';
              $row[] = ['data' => $cell_value];
            }

            $table_rows[] = $row;
          }
          else {
            $row = [];
            $row[] = ['data' => $type, 'header' => TRUE];
            $row[] = [
              'data' => $this->formatValue($array_item),
              'colspan' => count($column_keys),
            ];
            $table_rows[] = $row;
          }
        }
      }
      else {
        $row = [];
        $row[] = ['data' => $type, 'header' => TRUE];
        $row[] = [
          'data' => $this->formatValue($value),
          'colspan' => count($column_keys),
        ];
        $table_rows[] = $row;
      }
    }

    return [
      '#type' => 'table',
      '#rows' => $table_rows,
      '#header' => [],
      '#attributes' => ['class' => ['nanoboost-data-table']],
    ];
  }

  /**
   * Checks if the data has a position-based structure.
   *
   * @param array $data
   *   The data array to check.
   *
   * @return bool
   *   TRUE if the data has a position structure.
   */
  protected function hasPositionStructure(array $data) {
    foreach ($data as $key => $item_data) {
      if (is_numeric($key) && is_array($item_data) && isset($item_data[0])) {
        return TRUE;
      }
    }
    return FALSE;
  }

  /**
   * Checks if the data has a component-based structure.
   *
   * @param array $data
   *   The data array to check.
   *
   * @return bool
   *   TRUE if the data has a component structure.
   */
  protected function isComponentBasedStructure(array $data) {
    // Check if the data contains component UUIDs as keys.
    foreach ($data as $key => $item_data) {
      if (is_string($key) &&
          is_array($item_data) &&
          isset($item_data['uuid']) &&
          isset($item_data['completed']) &&
          isset($item_data['data'])) {
        return TRUE;
      }
    }
    return FALSE;
  }

  /**
   * Extracts data from component-based structure.
   *
   * @param array $components_data
   *   The components data array.
   *
   * @return array
   *   Flattened array of data items.
   */
  protected function extractDataFromComponents(array $components_data) {
    $extracted_data = [];

    foreach ($components_data as $component_data) {
      if (isset($component_data['data']) && is_array($component_data['data'])) {
        foreach ($component_data['data'] as $data_item) {
          if (isset($data_item['type']) && isset($data_item['value'])) {
            $extracted_data[] = $data_item;
          }
        }
      }
    }

    return $extracted_data;
  }

  /**
   * Formats a value for display in the table.
   *
   * @param mixed $value
   *   The value to format.
   *
   * @return string
   *   The formatted value.
   */
  protected function formatValue($value) {
    if (is_array($value)) {
      $formatted_values = [];
      foreach ($value as $item) {
        $formatted_values[] = $this->formatValue($item);
      }
      return implode(', ', $formatted_values);
    }
    elseif (is_bool($value)) {
      return $value ? 'Yes' : 'No';
    }
    elseif (is_null($value)) {
      return '';
    }
    elseif (is_object($value)) {
      return method_exists($value, '__toString') ? (string) $value : '';
    }
    else {
      return (string) $value;
    }
  }

}
