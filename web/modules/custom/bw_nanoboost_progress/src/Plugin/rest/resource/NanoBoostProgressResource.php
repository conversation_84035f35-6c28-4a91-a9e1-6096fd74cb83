<?php

namespace Drupal\bw_nanoboost_progress\Plugin\rest\resource;

use Drupal\bw_nanoboost_progress\Entity\NanoBoostProgress;
use Drupal\bw_token\Entity\Token;
use Drupal\bw_trophies\Entity\Trophy;
use Drupal\bw_user_branch_progress\UserBranchProgressService;
use Drupal\Core\Cache\Cache;
use Drupal\Core\Cache\CacheableJsonResponse;
use Drupal\Core\Cache\CacheableMetadata;
use Drupal\Core\Database\Connection;
use Drupal\Core\Entity\EntityStorageException;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Queue\QueueFactory;
use Drupal\Core\Session\AccountInterface;
use Drupal\node\NodeInterface;
use Drupal\profile\Entity\Profile;
use Drupal\rest\ModifiedResourceResponse;
use Drupal\rest\Plugin\ResourceBase;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Return the nanoboost progress.
 *
 * @RestResource(
 *   id = "bw_nanoboost_progress",
 *   label = @Translation("BW: NanoBoost: Progress"),
 *   uri_paths = {
 *     "canonical" = "/api/{version}/user/nano-frameworks/{nano_framework_id}/progress"
 *   },
 * )
 */
final class NanoBoostProgressResource extends ResourceBase {

  public const NANOBOOST_STATUS_NOT_STARTED = 'not_started';
  public const NANOBOOST_STATUS_IN_PROGRESS = 'in_progress';
  public const NANOBOOST_STATUS_COMPLETED = 'completed';

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  private AccountInterface $currentUser;

  /**
   * The database connection.
   *
   * @var \Drupal\Core\Database\Connection
   */
  private Connection $connection;

  /**
   * The user branch progress service.
   *
   * @var \Drupal\bw_user_branch_progress\UserBranchProgressService
   */
  private UserBranchProgressService $userBranchProgressService;

  /**
   * The queue factory.
   *
   * @var \Drupal\Core\Queue\QueueFactory
   */
  private QueueFactory $queueFactory;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param array $serializer_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   A logger instance.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   * @param \Drupal\Core\Database\Connection $connection
   *   The database connection.
   * @param \Drupal\bw_user_branch_progress\UserBranchProgressService $user_branch_progress_service
   *   The user branch progress service.
   * @param \Drupal\Core\Queue\QueueFactory $queue_factory
   *   The queue factory.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    array $serializer_formats,
    LoggerInterface $logger,
    EntityTypeManagerInterface $entity_type_manager,
    AccountInterface $current_user,
    Connection $connection,
    UserBranchProgressService $user_branch_progress_service,
    QueueFactory $queue_factory,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
    $this->entityTypeManager = $entity_type_manager;
    $this->currentUser = $current_user;
    $this->connection = $connection;
    $this->userBranchProgressService = $user_branch_progress_service;
    $this->queueFactory = $queue_factory;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('entity_type.manager'),
      $container->get('current_user'),
      $container->get('database'),
      $container->get('bw_user_branch_progress.service'),
      $container->get('queue'),
    );
  }

  /**
   * Get the nano-framework progress.
   *
   * @param string $nano_framework_id
   *   The nano-framework id.
   *
   * @return \Drupal\Core\Cache\CacheableJsonResponse
   *   The HTTP response object.
   */
  public function get(string $nano_framework_id): CacheableJsonResponse {
    /** @var \Drupal\node\Entity\Node $node */
    $node = $this->entityTypeManager->getStorage('node')->load($nano_framework_id);

    $this->validateNanoFrameworkNode($node);

    /** @var \Drupal\nanoboost_progress\Entity\NanoBoostProgress[] $nanoboost_progress */
    $nanoboost_progress = $this->entityTypeManager->getStorage('nanoboost_progress')->loadByProperties([
      'uid' => $this->currentUser->id(),
      'nbid' => $node->id(),
    ]);
    $nanoboost_progress = reset($nanoboost_progress);

    $cache = CacheableMetadata::createFromRenderArray([
      '#cache' => [
        'max-age' => Cache::PERMANENT,
        'tags' => Cache::mergeTags(
          $node->getCacheTags(),
          ["bw_nanoboost_progress:{$this->currentUser->id()}_{$node->id()}"],
        ),
        'contexts' => ['user'],
      ],
    ]);

    $response = $this->buildProgressResponse($node, $nanoboost_progress);

    return (new CacheableJsonResponse($response))
      ->addCacheableDependency($cache);
  }

  /**
   * Updates the nano-framework progress.
   *
   * @param string $nano_framework_id
   *   The nano-framework id.
   * @param \Symfony\Component\HttpFoundation\Request $request
   *   The HTTP request object.
   *
   * @return \Drupal\rest\ModifiedResourceResponse
   *   The HTTP response object.
   */
  public function put(string $nano_framework_id, Request $request): ModifiedResourceResponse {
    /** @var \Drupal\node\Entity\Node $node */
    $node = $this->entityTypeManager->getStorage('node')->load($nano_framework_id);

    $this->validateNanoFrameworkNode($node);

    $payload = json_decode($request->getContent(), TRUE);
    if (
      empty($payload)
      || !is_array($payload)
      || empty($payload['uuid'])
      || !is_string($payload['uuid'])
      || (
        !empty($payload['data'])
        && (
          !is_array($payload['data'])
          || strlen(serialize($payload['data'])) > 5000
        )
      )
    ) {
      throw new BadRequestHttpException('The request data is invalid. Required: uuid (string). Optional: data (array).');
    }

    // Validate that the component UUID exists in the node.
    $component_uuids = [];
    foreach ($node->field_components->referencedEntities() as $component) {
      $component_uuids[] = $component->uuid();
    }

    if (!in_array($payload['uuid'], $component_uuids)) {
      throw new BadRequestHttpException('The provided component UUID does not exist in this nano-framework.');
    }

    $student_profile = $this->entityTypeManager->getStorage('profile')
      ->loadByProperties([
        'type' => 'student',
        'uid' => $this->currentUser->id(),
      ]);
    $student_profile = reset($student_profile);

    if (!$student_profile) {
      throw new NotFoundHttpException('Student profile not found.');
    }

    $user_can_access_current_stage = $this->userBranchProgressService
      ->userCanAccessCurrentStage($node);
    if (!$user_can_access_current_stage) {
      throw new BadRequestHttpException("You have not unlocked the stage.");
    }

    // Load or create the nanoboost_progress entity.
    /** @var \Drupal\nanoboost_progress\Entity\NanoBoostProgress $nanoboost_progress */
    $nanoboost_progress = $this->entityTypeManager->getStorage('nanoboost_progress')->loadByProperties([
      'uid' => $this->currentUser->id(),
      'nbid' => $node->id(),
    ]);
    $nanoboost_progress = reset($nanoboost_progress);
    if (empty($nanoboost_progress)) {
      if (!$node->get("field_sz_access")->isEmpty()) {
        throw new BadRequestHttpException("You have not unlocked the nano-framework.");
      }
      $nanoboost_progress = $this->entityTypeManager->getStorage('nanoboost_progress')->create([
        'uid' => $this->currentUser->id(),
        'nbid' => $node->id(),
      ]);

      try {
        $nanoboost_progress->save();
      }
      catch (EntityStorageException $e) {
        // Possible duplicate due to race condition — reload.
        $nanoboost_progress = $this->entityTypeManager->getStorage('nanoboost_progress')->loadByProperties([
          'uid' => $this->currentUser->id(),
          'nbid' => $node->id(),
        ]);
        $nanoboost_progress = reset($nanoboost_progress);
        if (!$nanoboost_progress) {
          throw new \RuntimeException('Failed to create or load existing progress entity.');
        }
      }
    }

    $component_uuid = $payload['uuid'];
    // @todo what if it is video task?
    $component_completed = TRUE;
    $component_data = $payload['data'] ?? [];

    $transaction = $this->connection->startTransaction();
    try {
      // Get existing components data.
      $existing_components_data = $nanoboost_progress->get('data')->value ?? [];

      if (!is_array($existing_components_data)) {
        $existing_components_data = [];
      }

      // Update the specific component.
      $existing_components_data[$component_uuid] = [
        'uuid' => $component_uuid,
        'completed' => $component_completed,
        'data' => $component_data,
      ];

      // Save the updated components data.
      $nanoboost_progress->set('data', ['value' => $existing_components_data]);

      // Update user profile data if component data is provided.
      if (!empty($component_data)) {
        $this->updateUserNanoboostData($node, $student_profile, $component_data);
      }

      // Determine overall completion status based on components.
      $overall_status = $this->calculateOverallStatus($node, $existing_components_data);

      // @todo Grand rewards if Reward component.
      // If nano-framework is completed, award skills and rewards.
      if (self::NANOBOOST_STATUS_COMPLETED === $overall_status &&
          self::NANOBOOST_STATUS_COMPLETED !== $nanoboost_progress->status->value) {
        $this->updateSkillsAndRewards($node, $student_profile);

        // Award the trophy if it exists and is not already awarded.
        if (($trophy = $node->field_sz_trophy->entity) instanceof Trophy) {
          $user_trophy_storage = $this->entityTypeManager->getStorage('user_trophy');
          $user_trophies = $user_trophy_storage
            ->loadByProperties([
              'uid' => $this->currentUser->id(),
              'trophy' => $trophy->id(),
            ]);
          if (empty($user_trophies)) {
            $user_trophy_storage
              ->create([
                'uid' => $this->currentUser->id(),
                'trophy' => $trophy->id(),
                'date_acquired' => (new \DateTime())->getTimestamp(),
              ])->save();
          }
        }

        // Award the token if it exists and is not already awarded.
        if (($token = $node->field_sz_token->entity) instanceof Token) {
          $user_token_storage = $this->entityTypeManager->getStorage('user_token');
          $user_tokens = $user_token_storage
            ->loadByProperties([
              'uid' => $this->currentUser->id(),
              'token' => $token->id(),
            ]);
          if (empty($user_tokens)) {
            $user_token_storage
              ->create([
                'uid' => $this->currentUser->id(),
                'token' => $token->id(),
                'date_acquired' => (new \DateTime())->getTimestamp(),
              ])->save();
          }
        }
      }

      $nanoboost_progress->set('status', $overall_status);
      $nanoboost_progress->save();

      $this->userBranchProgressService->updateStageProgress($node);
    }
    catch (\Throwable $exception) {
      $transaction->rollBack();
      throw $exception;
    }

    unset($transaction);

    $response = $this->buildProgressResponse($node, $nanoboost_progress);

    return new ModifiedResourceResponse($response);
  }

  /**
   * Updates the skills and rewards.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The nanoboost node.
   * @param \Drupal\profile\Entity\Profile $student_profile
   *   The student profile.
   */
  private function updateSkillsAndRewards(NodeInterface $node, Profile $student_profile): void {
    // @todo Merge shortzzz projects save and updateSkillsAndRewards.
    $save_profile = FALSE;

    $nanoboost_skills = $node->field_sz_skills->referencedEntities();
    $profile_skills = $student_profile->field_s_skills->referencedEntities();
    $profile_skills_map = [];
    foreach ($profile_skills as $profile_skill) {
      $profile_skills_map[$profile_skill->field_ss_skill->value] = $profile_skill;
    }

    // Update or create skills.
    $paragraph_storage = $this->entityTypeManager->getStorage('paragraph');
    foreach ($nanoboost_skills as $nanoboost_skill) {
      $skill_name = $nanoboost_skill->field_sc_skill->value;
      $brayn_bits = (int) $nanoboost_skill->field_sc_braynbits->value;

      if (isset($profile_skills_map[$skill_name])) {
        // Update existing skill.
        $profile_skill = $profile_skills_map[$skill_name];
        $new_value = $brayn_bits + (int) $profile_skill->field_ss_braynbits->value;
        $profile_skill->set('field_ss_braynbits', $new_value);
      }
      else {
        // Create new skill.
        $profile_skill = $paragraph_storage->create([
          'type' => 'skill_student',
          'field_ss_braynbits' => $brayn_bits,
          'field_ss_skill' => $skill_name,
        ]);
        $student_profile->field_s_skills[] = $profile_skill;
      }
      $profile_skill->save();
      $save_profile = TRUE;
    }

    // Update rewards.
    $reward_fields = [
      'field_rewards_neurons' => 'field_s_neurons',
      'field_rewards_axon' => 'field_s_axons',
    ];

    foreach ($reward_fields as $node_field => $profile_field) {
      $reward = $node->get($node_field)->value ?? 0;
      $current_value = $student_profile->get($profile_field)->value ?? 0;
      $student_profile->set($profile_field, $current_value + $reward);
      $save_profile = TRUE;
    }

    if ($save_profile) {
      $student_profile->save();
    }
  }

  /**
   * Updates user profile with nanoboost data.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The nanoboost node.
   * @param \Drupal\profile\Entity\Profile $student_profile
   *   The student profile.
   * @param array $progress_data
   *   The nanoboost progress data.
   */
  private function updateUserNanoboostData(NodeInterface $node, Profile $student_profile, array $progress_data): void {
    // @todo Remove duplication with UserUpdateResource::updateNanoBoostData.
    $save_profile = FALSE;

    foreach ($progress_data as $item) {
      $field_name = 'field_' . $item['type'];

      if (!in_array($item['type'], ['bubble_quiz', 'dream_job', 'radar'])) {
        continue;
      }

      if ($student_profile->hasField($field_name)) {
        $field_data = $student_profile->get($field_name)->getValue();
        $field_data = !empty($field_data[0]['value']) ? unserialize($field_data[0]['value'], ['allowed_classes' => FALSE]) : [];

        $updated = FALSE;
        if (isset($field_data['id']) && $field_data['id'] == $node->id()) {
          $field_data['value'] = $item['value'];
          $updated = TRUE;
        }

        if (!$updated) {
          $field_data = [
            'id' => $node->id(),
            'value' => $item['value'],
          ];
        }

        $student_profile->set($field_name, serialize($field_data));
        $save_profile = TRUE;

        if ($field_name === 'field_radar') {
          // Check if user already has the BALANCE_MASTER trophy.
          $has_balance_master_trophy = (bool) $this->connection
            ->select('bw_user_trophy', 'ut')
            ->fields('ut', ['trophy'])
            ->condition('uid', $this->currentUser->id())
            ->condition('trophy', Trophy::TROPHY_ACTIVITY_BALANCE_MASTER . "_junior")
            ->countQuery()
            ->execute()
            ->fetchField();

          if (!$has_balance_master_trophy) {
            $this->queueFactory->get('bw_trophies_queue')->createItem([
              'id' => $this->currentUser->id(),
              'types' => [Trophy::TROPHY_ACTIVITY_BALANCE_MASTER],
            ]);
          }
        }
      }
    }

    if ($save_profile) {
      $student_profile->save();
    }
  }

  /**
   * Calculates the overall completion status based on component completion.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The nano-framework node.
   * @param array $components_data
   *   The components progress data.
   *
   * @return string
   *   The overall status (in_progress or completed).
   */
  private function calculateOverallStatus(NodeInterface $node, array $components_data): string {
    $total_components = 0;
    $completed_components = 0;

    foreach ($node->field_components->referencedEntities() as $component) {
      $total_components++;
      $component_uuid = $component->uuid();

      if (isset($components_data[$component_uuid]) &&
          $components_data[$component_uuid]['completed'] === TRUE) {
        $completed_components++;
      }
    }

    // If all components are completed, mark as completed.
    if ($total_components > 0 && $completed_components === $total_components) {
      return self::NANOBOOST_STATUS_COMPLETED;
    }

    return self::NANOBOOST_STATUS_IN_PROGRESS;
  }

  /**
   * Builds the progress response structure for both GET and PUT methods.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The nano-framework node.
   * @param \Drupal\nanoboost_progress\Entity\NanoBoostProgress|false $progress
   *   Nanoboost progress entity or false.
   *
   * @return array
   *   The response array with id, status, and components.
   */
  private function buildProgressResponse(NodeInterface $node, NanoBoostProgress|false $progress): array {
    // Get stored components data if progress exists, otherwise use empty array.
    $stored_components_data = [];
    $status = self::NANOBOOST_STATUS_NOT_STARTED;

    if (!empty($progress)) {
      $stored_components_data = $progress->get("data")->value ?? [];
      $status = $progress->status->value;
    }

    // Always build components array from the node's components.
    $components = [];
    foreach ($node->field_components->referencedEntities() as $position => $component) {
      $component_uuid = $component->uuid();
      $component_data = $stored_components_data[$component_uuid] ?? [];

      $components[] = [
        'uuid' => $component_uuid,
        'position' => $position + 1,
        'type' => $component->bundle(),
        'completed' => $component_data['completed'] ?? FALSE,
        'data' => $component_data['data'] ?? [],
      ];
    }

    return [
      'id' => (int) $node->id(),
      'status' => $status,
      'components' => $components,
    ];
  }

  /**
   * Validates the nano-framework node exists, is published, and is shortzzz.
   *
   * @param \Drupal\node\NodeInterface|null $node
   *   The node to validate.
   *
   * @throws \Symfony\Component\HttpKernel\Exception\NotFoundHttpException
   *   If the node is invalid.
   */
  private function validateNanoFrameworkNode(?NodeInterface $node): void {
    if (
      !$node
      || !$node->isPublished()
      || $node->bundle() !== 'shortzzz'
    ) {
      throw new NotFoundHttpException('The nano-framework could not be found or is not published.');
    }
  }

}
