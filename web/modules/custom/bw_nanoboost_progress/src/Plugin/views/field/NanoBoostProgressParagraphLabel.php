<?php

namespace Drupal\bw_nanoboost_progress\Plugin\views\field;

use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\views\Plugin\views\field\FieldPluginBase;
use Drupal\views\ResultRow;
use <PERSON>ymfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides a custom field to display paragraph label based on position.
 *
 * @ViewsField("nanoboost_progress_paragraph_label")
 */
class NanoBoostProgressParagraphLabel extends FieldPluginBase {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * Constructs a new NanoBoostProgressParagraphLabel object.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager service.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    EntityTypeManagerInterface $entity_type_manager,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('entity_type.manager')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function query() {
    $this->ensureMyTable();
    $this->addAdditionalFields(['data', 'nbid']);
  }

  /**
   * {@inheritdoc}
   */
  public function render(ResultRow $values) {
    /** @var \Drupal\nanoboost_progress\Entity\NanoBoostProgress $entity */
    $entity = $values->_entity;
    if (!$entity) {
      return $this->t('No entity');
    }

    if (!$entity->hasField('data')) {
      return $this->t('No data field');
    }

    if (!$entity->hasField('nbid')) {
      return $this->t('No nbid field');
    }

    $nbid = $entity->get('nbid')->target_id;
    if (empty($nbid)) {
      return $this->t('No nanoboost reference');
    }

    /** @var \Drupal\node\Entity\Node $node */
    $node = $this->entityTypeManager->getStorage('node')->load($nbid);
    if (!$node) {
      return $this->t('Nanoboost not found');
    }

    if (!$node->hasField('field_components')) {
      return $this->t('No components field');
    }

    // Get the components data from the progress entity.
    $components_data = $entity->get('data')->value ?? [];
    if (!is_array($components_data)) {
      $components_data = [];
    }

    // Get all components from the node.
    /** @var \Drupal\Core\Field\EntityReferenceFieldItemList $field */
    $field = $node->get('field_components');
    $components = $field->referencedEntities();
    if (empty($components)) {
      return $this->t('No components');
    }

    // Find the first incomplete component.
    /** @var \Drupal\paragraphs\ParagraphInterface $component */
    foreach ($components as $component) {
      $component_uuid = $component->uuid();

      // Check if this component is completed.
      if (!isset($components_data[$component_uuid]) ||
          !($components_data[$component_uuid]['completed'] ?? FALSE)) {
        // This is the current step - return its label.
        return $component->getParagraphType()->label();
      }
    }

    // All components are completed.
    return $this->t('Completed');
  }

}
