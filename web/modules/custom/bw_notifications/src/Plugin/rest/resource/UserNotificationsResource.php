<?php

namespace Drupal\bw_notifications\Plugin\rest\resource;

use <PERSON><PERSON>al\Core\Cache\CacheBackendInterface;
use <PERSON><PERSON>al\Core\Database\Connection;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON>upal\Core\Session\AccountInterface;
use Drupal\rest\ModifiedResourceResponse;
use Drupal\rest\Plugin\ResourceBase;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Return the user notifications data.
 *
 * @RestResource(
 *   id = "bw_user_notifications",
 *   label = @Translation("BW: User: Notifications"),
 *   uri_paths = {
 *     "canonical" = "/api/{version}/user/notifications",
 *   },
 * )
 */
final class UserNotificationsResource extends ResourceBase {

  private const NOTIFICATION_TYPE = [
    'comment_tag',
    'level_rewards',
    'follower',
    'certification_received',
    'challenge_rewards',
    'course_progress_inactivity_25',
    'course_progress_inactivity_50',
    'course_progress_inactivity_75',
    'shortzzz_project_banned',
    'shortzzz_project_approved',
    'shortzzz_project_featured',
    'shortzzz_project_declined',
    'shortzzz_project_like',
    'smart_goal_three_days_in_advance',
    'smart_goal_halfway',
    'smart_goal_end_day',
    'new_course_or_shortzzz',
    'new_shortzzz',
    'new_big5',
  ];

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  private AccountInterface $currentUser;

  /**
   * The database connection.
   *
   * @var \Drupal\Core\Database\Connection
   */
  private Connection $connection;

  /**
   * The cache interface.
   *
   * @var \Drupal\Core\Cache\CacheBackendInterface
   */
  private CacheBackendInterface $cacheBackend;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param array $serializer_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   A logger instance.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   * @param \Drupal\Core\Database\Connection $connection
   *   The database connection.
   * @param \Drupal\Core\Cache\CacheBackendInterface $cacheBackend
   *   The cache interface.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    array $serializer_formats,
    LoggerInterface $logger,
    AccountInterface $current_user,
    Connection $connection,
    CacheBackendInterface $cacheBackend,
    EntityTypeManagerInterface $entity_type_manager,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
    $this->currentUser = $current_user;
    $this->connection = $connection;
    $this->cacheBackend = $cacheBackend;
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('current_user'),
      $container->get('database'),
      $container->get('cache.default'),
      $container->get('entity_type.manager'),
    );
  }

  /**
   * Get the user courses data.
   *
   * @return \Drupal\rest\ModifiedResourceResponse
   *   The HTTP response object.
   */
  public function get(): ModifiedResourceResponse {
    if (($data = $this->cacheBackend->get("bw_user_notifications:{$this->currentUser->id()}"))) {
      return new ModifiedResourceResponse($data->data);
    }

    /** @var \Drupal\user\Entity\User $user */
    $user = $this->entityTypeManager
      ->getStorage('user')
      ->load($this->currentUser->id());

    $data = array_map(
      fn ($notification) => [
        'nid' => $notification['nid'],
        'title' => (string) $this->getNotificationTitle($notification['type']),
        'description' => $notification['description'],
        'date_created' => (new \DateTime())
          ->setTimestamp($notification['date_created'])
          ->format('Y-m-d\TH:i:s\Z'),
      ],
      $this->connection
        ->select('bw_notifications_user_data', 'un')
        ->fields('un', ['nid', 'type', 'description', 'date_created'])
        ->condition('un.uid', [$this->currentUser->id(), 0], 'IN')
        ->condition('un.type', self::NOTIFICATION_TYPE, 'IN')
        ->condition('un.date_created', $user->getCreatedTime(), '>=')
        ->orderBy('date_created', 'desc')
        ->execute()
        ->fetchAll(\PDO::FETCH_ASSOC)
    );

    $this->cacheBackend->set(
      "bw_user_notifications:{$this->currentUser->id()}",
      $data,
      (new \DateTime())->getTimestamp() + (5 * 60),
      [
        "bw_user_notifications",
        "bw_user_notifications:{$this->currentUser->id()}",
      ],
    );

    return new ModifiedResourceResponse($data);
  }

  /**
   * Get the notification title based on the type.
   *
   * @param string $type
   *   The notification type.
   *
   * @return string
   *   The notification title.
   */
  private function getNotificationTitle(string $type): string {
    return [
      'comment_tag' => $this->t('When being tagged in a comment:'),
      'level_rewards' => $this->t('Raising user level:'),
      'follower' => $this->t('Getting a new follower:'),
      'certification_received' => $this->t('You’ve got a Token!'),
      'challenge_rewards' => $this->t('Challenge rewards:'),
      'course_progress_inactivity_25' => $this->t('Hurry up to complete the module:'),
      'course_progress_inactivity_50' => $this->t('Hurry up to complete the module:'),
      'course_progress_inactivity_75' => $this->t('Hurry up to complete the module:'),
      'shortzzz_project_banned' => $this->t('Project banned:'),
      'shortzzz_project_approved' => $this->t('Project approved:'),
      'shortzzz_project_featured' => $this->t('Project featured:'),
      'shortzzz_project_declined' => $this->t('Project declined:'),
      'shortzzz_project_like' => $this->t('The project video was liked:'),
      'smart_goal_three_days_in_advance' => $this->t('3 days to go—you’re close!'),
      'smart_goal_halfway' => $this->t('Look at you—halfway done!'),
      'smart_goal_end_day' => $this->t('Goal Day! Let’s finish strong!'),
      'new_course_or_shortzzz' => $this->t('A new module alert:'),
      'new_shortzzz' => $this->t('New nano-boost:'),
      'new_big5' => $this->t('New assessment is available:'),
    ][$type];
  }

}
