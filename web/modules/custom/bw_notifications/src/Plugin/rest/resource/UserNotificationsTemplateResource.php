<?php

namespace Drupal\bw_notifications\Plugin\rest\resource;

use <PERSON>upal\bw_notifications\NotificationService;
use <PERSON><PERSON>al\bw_security\Encoder;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\rest\ModifiedResourceResponse;
use Drupal\rest\Plugin\ResourceBase;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Return the user notifications data.
 *
 * @RestResource(
 *   id = "bw_push_notifications_test",
 *   label = @Translation("BW: Push notifications test"),
 *   uri_paths = {
 *     "create" = "/api/{version}/push-notifications-test",
 *   },
 * )
 */
final class UserNotificationsTemplateResource extends ResourceBase {

  private const NOTIFICATION_TYPE = [
    'comment_tag',
    'level_rewards',
    'follower',
    'certification_received',
    'challenge_rewards',
    'course_progress_inactivity_25',
    'course_progress_inactivity_50',
    'course_progress_inactivity_75',
    'shortzzz_project_banned',
    'shortzzz_project_approved',
    'shortzzz_project_featured',
    'shortzzz_project_declined',
    'shortzzz_project_like',
    'smart_goal_three_days_in_advance',
    'smart_goal_halfway',
    'smart_goal_end_day',
    'new_course_or_shortzzz',
    'new_shortzzz',
    'new_big5',
  ];

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * Notification service.
   *
   * @var \Drupal\bw_notifications\NotificationService
   */
  protected NotificationService $notification;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param array $serializer_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   A logger instance.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\bw_notifications\NotificationService $notification
   *   Notification service.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    array $serializer_formats,
    LoggerInterface $logger,
    EntityTypeManagerInterface $entity_type_manager,
    NotificationService $notification,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
    $this->entityTypeManager = $entity_type_manager;
    $this->notification = $notification;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('entity_type.manager'),
      $container->get('bw_notification')
    );
  }

  /**
   * Create notification from template.
   *
   * @param \Symfony\Component\HttpFoundation\Request $request
   *   The HTTP request object.
   *
   * @return \Drupal\rest\ModifiedResourceResponse
   *   The HTTP response object.
   */
  public function post(Request $request): ModifiedResourceResponse {
    $request_data = json_decode($request->getContent(), TRUE);

    if (!is_string($request_data['data'])) {
      throw new BadRequestHttpException('The request data is invalid.');
    }
    $data = (new Encoder())->decode($request_data['data']);
    $data_parts = explode('/', $data);
    $user = $this->entityTypeManager
      ->getStorage('user')
      ->load((int) $data_parts[0]);
    if (!$user) {
      throw new NotFoundHttpException('The user could not be found.');
    }
    $notification_type = $data_parts[1];
    if (!in_array($notification_type, self::NOTIFICATION_TYPE)) {
      throw new BadRequestHttpException('The provided notification type is
    invalid.');
    }

    $title = '';
    $notification_data = ['type' => $notification_type];

    switch ($notification_type) {
      case 'comment_tag':
        $title = $this->t("🌟 Got tagged? Tap in and see what's buzzing! 🎤");
        $notification_data['comment_id'] = 999999;
        break;

      case 'level_rewards':
        $title = $this->t("🚀 You’ve leveled up! Stay awesome—your dream job awaits!");
        $notification_data['level_rewards'] = [
          'neurons' => 999999,
        ];
        break;

      case 'follower':
        $title = $this->t("🦋 New follower! Your squad’s growing 🤝");
        $notification_data['user_id'] = 999999;
        break;

      case 'certification_received':
        $title = $this->t("Congrats! You’ve unlocked a skill-proof token!");
        $notification_data['course_id'] = 999999;
        break;

      case 'challenge_rewards':
        $title = $this->t('Junior 999999 unlocked!');
        $notification_data['challenge_rewards'] = [
          'neurons' => 999999,
        ];
        $notification_data['image'] = NULL;
        break;

      case 'course_progress_inactivity_25':
        $title = $this->t('🎈 25% done! Almost there—keep going for your dream career 🚀');
        $notification_data['course_id'] = 999999;
        break;

      case 'course_progress_inactivity_50':
        $title = $this->t('💪 Halfway there! Keep at it to reach your full potential!');
        $notification_data['course_id'] = 999999;
        break;

      case 'course_progress_inactivity_75':
        $title = $this->t("🎉 75% done! You're crushing it! Rewards are coming 💪");
        $notification_data['course_id'] = 999999;
        break;

      case 'shortzzz_project_banned':
        $title = $this->t("⚠️ Your video was banned for inappropriate content. Please revise and resubmit.");
        $notification_data['shortzzz_id'] = 999999;
        break;

      case 'shortzzz_project_approved':
        $title = $this->t("✨ Shortzzz is live! Can't wait to see the impact you make! 🌍");
        $notification_data['shortzzz_project_id'] = 999999;
        break;

      case 'shortzzz_project_featured':
        $title = $this->t("🏆 Spotlight on you! Your shortzzz's a hit. Respect! 👏");
        $notification_data['shortzzz_project_id'] = 999999;
        break;

      case 'shortzzz_project_declined':
        $title = $this->t("�� Sorry, your video was rejected. No worries—feel free to try again!");
        $notification_data['shortzzz_id'] = 999999;
        break;

      case 'shortzzz_project_like':
        $title = $this->t("🌟 The likes keep rolling in—you’re an awesome content creator!");
        $notification_data['shortzzz_project_id'] = 999999;
        break;

      case 'smart_goal_three_days_in_advance':
        $title = $this->t("3 days left to hit your goal! Keep pushing! 💪");
        break;

      case 'smart_goal_halfway':
        $title = $this->t("You’re halfway to your goal! Let’s keep the vibe going! 🚀");
        break;

      case 'smart_goal_end_day':
        $title = $this->t("Today’s the day! Time to crush your goal! 💥");
        break;

      case 'new_course_or_shortzzz':
        $title = $this->t("🧠 New module available! Boost your skills and get closer to your dream job 🤟");
        $notification_data['shortzzz_project_id'] = NULL;
        break;

      case 'new_shortzzz':
        $title = $this->t("🚀 Check out the new nano-boost. Available now!");
        $notification_data['shortzzz_project_id'] = 98;
        break;

      case 'new_big5':
        $title = $this->t("Discover your potential with BRAYN Scan. Check out this new feature!");
        $notification_data['big5_id'] = 92;
        break;

      default:
    }

    $this->notification->send(
      $this->entityTypeManager
        ->getStorage('profile')
        ->loadByUser($user, 'student'),
      [
        'contents' => ['en' => $title],
        'include_external_user_ids' => [$user->uuid()],
        'data' => $notification_data,
      ]
    );

    return new ModifiedResourceResponse(TRUE, 201);
  }

}
