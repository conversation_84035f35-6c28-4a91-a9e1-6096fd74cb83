services:
  bw_rest_resources.serializer.shortzzz_project:
    class: Drupal\bw_rest_resources\ShortzzzProjectSerializer
    arguments:
      - '@bw_rest_resources.serializer.user'
      - '@bw_rest_resources.serializer.node'
      - '@bw_vimeo.client'
  bw_rest_resources.serializer.node:
    class: Drupal\bw_rest_resources\NodeSerializer
    arguments:
      - '@bw_rest_resources.serializer.user'
      - '@bw_vimeo.client'
      - '@file_url_generator'
      - '@database'
      - '@current_user'
      - '@entity_type.manager'
  bw_rest_resources.serializer.user_branch_progress:
    class: Drupal\bw_rest_resources\UserBranchProgressSerializer
    arguments:
      - '@entity_type.manager'
  bw_rest_resources.serializer.user:
    class: Drupal\bw_rest_resources\UserSerializer
    arguments:
      - '@entity_type.manager'
      - '@file_url_generator'
      - '@current_user'
      - '@bw_rest_resources.serializer.user_smart_goal'
  bw_rest_resources.serializer.user_smart_goal:
    class: Drupal\bw_rest_resources\UserSmartGoalSerializer
    arguments:
      - '@entity_type.manager'
      - '@file_url_generator'
      - '@current_user'
  bw_rest_resources.http_middleware:
    class: Drupal\bw_rest_resources\Middleware\HttpMiddleware
    arguments:
      - '@kernel'
    tags:
      - { name: http_middleware, priority: 999 }
  bw_rest_resources.response_subscriber:
    class: Drupal\bw_rest_resources\EventSubscriber\ResponseSubscriber
    tags:
      - { name: event_subscriber }
