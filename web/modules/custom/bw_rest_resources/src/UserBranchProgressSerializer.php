<?php

namespace Drupal\bw_rest_resources;

use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON><PERSON>\node\NodeInterface;
use <PERSON><PERSON><PERSON>\user\UserInterface;

/**
 * Class used for serializing/deserializing of user branch progress.
 */
final class UserBranchProgressSerializer {
  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * Class constructor.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   */
  public function __construct(
    EntityTypeManagerInterface $entity_type_manager,
  ) {
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * Deserializes node entities.
   *
   * @param \Drupal\user\UserInterface $user
   *   The user that needs to be deserialized.
   * @param \Drupal\node\NodeInterface $node
   *   The node that needs to be deserialized.
   *
   * @return array
   *   The array of node data.
   */
  public function deserialize(
    UserInterface $user,
    NodeInterface $node,
  ): array {
    $nano_frameworks = array_merge($node->field_st1_nano_frameworks->referencedEntities(), $node->field_st2_nano_frameworks->referencedEntities(), $node->field_st3_nano_frameworks->referencedEntities(), $node->field_st4_nano_frameworks->referencedEntities());
    $data = [
      'id' => (int) $node->id(),
      'title' => $node->label(),
      'color' => $node->field_branch_color->value,
      'total_braynbits' => (int) $this->calculateTotalBraynbits($nano_frameworks),
      'received_braynbits' => (int) $this->calculateReceivedBraynbits($user, $nano_frameworks),
      'tokens' => [],
    ];

    foreach ($nano_frameworks as $nano_framework) {
      if ($nano_framework->bundle() === 'shortzzz' && !$nano_framework->field_sz_token->isEmpty()) {
        $token = $nano_framework->field_sz_token->entity;
        $data['tokens'][] = [
          'id' => $token->id(),
          'title' => $token->label(),
          'description' => $token->get('description')->value,
          'received' => (bool) $this->entityTypeManager
            ->getStorage('user_token')
            ->loadByProperties([
              'uid' => $user->id(),
              'token' => $token->id(),
            ]),
          'icon' => $token->icon_fid->entity && ($icon_uri = $token->icon_fid?->entity?->field_media_image_1?->entity->createFileUrl()) ? $icon_uri : NULL,
          'icon_grey' => $token->icon_gray_fid->entity && ($icon_gray_uri = $token->icon_gray_fid?->entity?->field_media_image_1?->entity->createFileUrl()) ? $icon_gray_uri : NULL,
        ];
      }
    }

    if (!$node->field_st1_token->isEmpty()) {
      $token = $node->field_st1_token->entity;
      $data['tokens'][] = [
        'id' => $token->id(),
        'title' => $token->label(),
        'description' => $token->get('description')->value,
        'received' => (bool) $this->entityTypeManager
          ->getStorage('user_token')
          ->loadByProperties([
            'uid' => $user->id(),
            'token' => $token->id(),
          ]),
        'icon' => $token->icon_fid->entity && ($icon_uri = $token->icon_fid?->entity?->field_media_image_1?->entity->createFileUrl()) ? $icon_uri : NULL,
        'icon_grey' => $token->icon_gray_fid->entity && ($icon_gray_uri = $token->icon_gray_fid?->entity?->field_media_image_1?->entity->createFileUrl()) ? $icon_gray_uri : NULL,
      ];
    }

    if (!$node->field_branch_token->isEmpty()) {
      $token = $node->field_branch_token->entity;
      $data['tokens'][] = [
        'id' => $token->id(),
        'title' => $token->label(),
        'description' => $token->get('description')->value ?? '',
        'received' => (bool) $this->entityTypeManager
          ->getStorage('user_token')
          ->loadByProperties([
            'uid' => $user->id(),
            'token' => $token->id(),
          ]),
        'icon' => $token->icon_fid->entity && ($icon_uri = $token->icon_fid?->entity?->field_media_image_1?->entity->createFileUrl()) ? $icon_uri : NULL,
        'icon_grey' => $token->icon_gray_fid->entity && ($icon_gray_uri = $token->icon_gray_fid?->entity?->field_media_image_1?->entity->createFileUrl()) ? $icon_gray_uri : NULL,
      ];
    }

    return $data;
  }

  /**
   * Calculates total braynbits for the given nano-frameworks.
   *
   * @param array $nano_frameworks
   *   Array of node entities.
   *
   * @return int
   *   The total braynbits.
   */
  private function calculateTotalBraynbits(array $nano_frameworks): int {
    return array_reduce($nano_frameworks, function ($sum, $nano_framework) {
      if (!($nano_framework instanceof NodeInterface)) {
        return $sum;
      }

      $skills = $nano_framework->bundle() === 'shortzzz'
        ? $nano_framework->field_sz_skills->referencedEntities()
        : $nano_framework->field_cs1_skills->referencedEntities();

      // For shortzzz type, add braynbits from reward components.
      if ($nano_framework->bundle() === 'shortzzz' && !$nano_framework->field_components->isEmpty()) {
        foreach ($nano_framework->field_components->referencedEntities() as $component) {
          if ($component->bundle() === 'reward' && !$component->field_skills->isEmpty()) {
            $skills = array_merge($skills, $component->field_skills->referencedEntities());
          }
        }
      }

      return $sum + array_reduce($skills,
        fn ($skill_sum, $skill) => $skill_sum + (int) $skill->field_sc_braynbits->value,
        0
      );
    }, 0);
  }

  /**
   * Calculates received braynbits for completed nano-frameworks.
   *
   * @param \Drupal\user\UserInterface $user
   *   The user object.
   * @param array $nano_frameworks
   *   Array of node entities.
   *
   * @return int
   *   The received braynbits.
   */
  private function calculateReceivedBraynbits($user, array $nano_frameworks): int {
    return array_reduce($nano_frameworks, function ($sum, $nano_framework) use ($user) {
      if (!($nano_framework instanceof NodeInterface)) {
        return $sum;
      }

      // Get progress entity.
      $progress = $this->entityTypeManager
        ->getStorage('nanoboost_progress')
        ->loadByProperties([
          'uid' => $user->id(),
          'nbid' => $nano_framework->id(),
        ]);

      $skills = [];
      if (!empty($progress)) {
        /** @var \Drupal\nanoboost_progress\Entity\NanoBoostProgress $progress */
        $progress = reset($progress);
        // Add skills rewarded on completion.
        if ($progress->status->value === 'completed') {
          $skills = $nano_framework->field_sz_skills->referencedEntities();
        }

         // Add skills from reward components.
        if (!$nano_framework->field_components->isEmpty()) {
          foreach ($nano_framework->field_components->referencedEntities() as $component) {
            if ($component->bundle() === 'reward' && !$component->field_skills->isEmpty()) {
              $stored_components_data = $progress->get("data")->value ?? [];
              $component_data = $stored_components_data[$component->uuid()] ?? [];
              if ($component_data['completed']) {
                $skills = array_merge($skills, $component->field_skills->referencedEntities());
              }
            }
          }
        }
      }

      return $sum + array_reduce($skills,
        fn ($skill_sum, $skill) => $skill_sum + (int) $skill->field_sc_braynbits->value,
        0
      );
    }, 0);
  }

}
