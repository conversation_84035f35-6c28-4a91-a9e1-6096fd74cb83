<?php

namespace Drupal\bw_security\Plugin\rest\resource;

use <PERSON><PERSON><PERSON>\Core\Cache\Cache;
use <PERSON><PERSON>al\Core\Database\Connection;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON><PERSON>\Core\Session\AccountInterface;
use <PERSON><PERSON><PERSON>\file\FileInterface;
use <PERSON><PERSON>al\rest\ModifiedResourceResponse;
use Drupal\rest\Plugin\ResourceBase;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpKernel\Exception\HttpException;

/**
 * Deletes an existing user.
 *
 * @RestResource(
 *   id = "bw_user_delete",
 *   label = @Translation("BW: User: Delete"),
 *   uri_paths = {
 *     "canonical" = "/api/{version}/user/delete",
 *   },
 * )
 */
final class UserDeleteResource extends ResourceBase {

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  private AccountInterface $currentUser;

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * The database connection.
   */
  protected Connection $database;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param array $serializer_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   A logger instance.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Database\Connection $database
   *   The database connection.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    array $serializer_formats,
    LoggerInterface $logger,
    AccountInterface $current_user,
    EntityTypeManagerInterface $entity_type_manager,
    Connection $database,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
    $this->currentUser = $current_user;
    $this->entityTypeManager = $entity_type_manager;
    $this->database = $database;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('current_user'),
      $container->get('entity_type.manager'),
      $container->get('database'),
    );
  }

  /**
   * Deletes the current user.
   *
   * @return \Drupal\rest\ModifiedResourceResponse
   *   The HTTP response object.
   */
  public function delete(): ModifiedResourceResponse {
    $transaction = $this->database->startTransaction();

    try {
      $id = $this->currentUser->id();
      /** @var \Drupal\user\Entity\User $user */
      $user = $this->entityTypeManager->getStorage('user')->load($id);

      if (function_exists('bw_user_shortzzz_project_entity_delete')) {
        bw_user_shortzzz_project_entity_delete($user);
      }

      $user->block();
      $user->setUsername("deleted__{$id}");
      $user->set('field_ios_user_id', $user->getAccountName());
      $user->setEmail("deleted__{$id}@brayn.app");
      $user->setPassword(uniqid() . '-' . uniqid());
      $user->save();

      Cache::invalidateTags(["bw_user_market_boxes:{$id}"]);

      /** @var \Drupal\profile\ProfileStorage $profile_storage */
      $profile_storage = \Drupal::entityTypeManager()->getStorage('profile');
      $profile = $profile_storage->loadByUser($user, 'student');

      $file = $profile->field_s_avatar->entity;

      $profile->set('field_s_nickname', $user->getAccountName());
      $profile->save();

      if ($file instanceof FileInterface) {
        $file->delete();
      }

      unset($transaction);

      return new ModifiedResourceResponse(NULL, 204);
    }
    catch (\Exception $e) {
      $transaction->rollBack();
      $this->logger->error('User deletion failed: @message', ['@message' => $e->getMessage()]);
      throw new HttpException(500, 'Unable to process user delete request. Please try again later.');
    }
  }

}
