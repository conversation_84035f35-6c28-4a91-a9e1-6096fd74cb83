<?php

namespace Drupal\bw_security\Plugin\rest\resource;

use Dr<PERSON>al\bw_nanoboost_progress\NanoboostDataService;
use Drupal\bw_onesignal\OneSignalService;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Language\LanguageManagerInterface;
use Drupal\Core\Lock\LockBackendInterface;
use Drupal\Core\Mail\MailManagerInterface;
use Drupal\Core\Password\PasswordInterface;
use Drupal\Core\Session\AccountInterface;
use Drupal\rest\ModifiedResourceResponse;
use Drupal\rest\Plugin\ResourceBase;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

/**
 * Edit the user.
 *
 * @RestResource(
 *   id = "bw_user_update",
 *   label = @Translation("BW: User: Update"),
 *   uri_paths = {
 *     "canonical" = "/api/{version}/user/update"
 *   },
 * )
 */
final class UserUpdateResource extends ResourceBase {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  private AccountInterface $currentUser;

  /**
   * The password hashing service.
   *
   * @var \Drupal\Core\Password\PasswordInterface
   */
  private PasswordInterface $passwordChecker;

  /**
   * OneSignal service.
   *
   * @var \Drupal\bw_onesignal\OneSignalService
   */
  protected OneSignalService $oneSignal;

  /**
   * The mail manager.
   *
   * @var \Drupal\Core\Mail\MailManagerInterface
   */
  private MailManagerInterface $mailManager;

  /**
   * The language manager.
   *
   * @var \Drupal\Core\Language\LanguageManagerInterface
   */
  private LanguageManagerInterface $languageManager;

  /**
   * The lock.
   *
   * @var \Drupal\Core\Lock\LockBackendInterface
   */
  private LockBackendInterface $lock;

  /**
   * The nanoboost data service.
   *
   * @var \Drupal\bw_nanoboost_progress\NanoboostDataService
   */
  private NanoboostDataService $nanoboostDataService;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param array $serializer_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   A logger instance.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   * @param \Drupal\Core\Password\PasswordInterface $password_checker
   *   The password service.
   * @param \Drupal\bw_onesignal\OneSignalService $one_signal
   *   OneSignal service.
   * @param \Drupal\Core\Mail\MailManagerInterface $mail_manager
   *   The mail manager.
   * @param \Drupal\Core\Language\LanguageManagerInterface $language_manager
   *   The language manager.
   * @param \Drupal\Core\Lock\LockBackendInterface $lock
   *   The lock.
   * @param \Drupal\bw_nanoboost_progress\NanoboostDataService $nanoboost_data_service
   *   The nanoboost data service.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    array $serializer_formats,
    LoggerInterface $logger,
    EntityTypeManagerInterface $entity_type_manager,
    AccountInterface $current_user,
    PasswordInterface $password_checker,
    OneSignalService $one_signal,
    MailManagerInterface $mail_manager,
    LanguageManagerInterface $language_manager,
    LockBackendInterface $lock,
    NanoboostDataService $nanoboost_data_service,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
    $this->entityTypeManager = $entity_type_manager;
    $this->currentUser = $current_user;
    $this->passwordChecker = $password_checker;
    $this->oneSignal = $one_signal;
    $this->mailManager = $mail_manager;
    $this->languageManager = $language_manager;
    $this->lock = $lock;
    $this->nanoboostDataService = $nanoboost_data_service;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('entity_type.manager'),
      $container->get('current_user'),
      $container->get('password'),
      $container->get('bw_onesignal'),
      $container->get('plugin.manager.mail'),
      $container->get('language_manager'),
      $container->get('lock'),
      $container->get('bw_nanoboost_progress.nanoboost_data_service'),
    );
  }

  /**
   * Edit the user data.
   *
   * @param \Symfony\Component\HttpFoundation\Request $request
   *   The HTTP request object.
   *
   * @return \Drupal\rest\ModifiedResourceResponse
   *   The HTTP response object.
   */
  public function patch(Request $request): ModifiedResourceResponse {
    $response = json_decode($request->getContent(), TRUE);
    if (!is_array($response) || empty($response)) {
      throw new BadRequestHttpException('The request data is invalid.');
    }

    $user_storage = $this->entityTypeManager->getStorage('user');
    /** @var \Drupal\user\Entity\User $user */
    $user = $user_storage->load($this->currentUser->id());

    /** @var \Drupal\profile\ProfileStorage $profile_storage */
    $profile_storage = $this->entityTypeManager->getStorage('profile');
    $profile = $profile_storage->loadByUser($this->currentUser, 'student');

    $lock_name = 'profile_save_' . $profile->id();
    $max_attempts = 5;
    $attempt = 0;

    while ($attempt < $max_attempts) {
      if ($this->lock->acquire($lock_name)) {
        $save_user = $save_profile = FALSE;
        $email_provided = array_key_exists('mail', $response);
        if ($profile->field_is_temporary->value) {
          $ios_user_provided = array_key_exists('field_ios_user_id', $response);

          if ($ios_user_provided) {
            if (!is_string($response['field_ios_user_id'])) {
              $this->lock->release($lock_name);
              throw new BadRequestHttpException('The request data is invalid.');
            }

            $user->set('field_ios_user_id', $response['field_ios_user_id']);
            $profile->set('field_is_temporary', 0);
            $save_user = $save_profile = TRUE;
          }

          if (
            $email_provided
            && $response['mail'] !== $user->getEmail()
          ) {
            if (!is_string($response['mail'])) {
              $this->lock->release($lock_name);
              throw new BadRequestHttpException('The request data is invalid.');
            }

            $user->set('mail', $response['mail']);
            $save_user = TRUE;
          }
        }
        if (
          $email_provided
          && $response['mail'] !== $user->getEmail()
        ) {
          if (!is_string($response['mail'])) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The request data is invalid.');
          }

          $user->set('mail', $response['mail']);
          $save_user = TRUE;
        }
        if (array_key_exists('skills_order', $response)) {
          if (!is_array($response['skills_order'])) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The request data is invalid.');
          }

          $skills = bw_misc_interests_allowed_values_function();

          foreach ($response['skills_order'] as $skill) {
            if (!is_string($skill) || !isset($skills[$skill])) {
              $this->lock->release($lock_name);
              throw new BadRequestHttpException('Invalid skill provided.');
            }
          }

          $profile->set('field_s_skills_order', $response['skills_order']);
          $save_profile = TRUE;
        }

        if (array_key_exists('gender', $response)) {
          if (
            !is_string($response['gender'])
            || !array_key_exists(
              $response['gender'],
              $profile->field_s_gender->getSetting('allowed_values'),
            )
          ) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The request data is invalid.');
          }
          $profile->set('field_s_gender', $response['gender']);
          $save_profile = TRUE;
        }

        if (array_key_exists('interests', $response)) {
          if (!is_array($response['interests'])) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The request data is invalid.');
          }

          $interests = bw_misc_interests_allowed_values_function();

          foreach ($response['interests'] as $interest) {
            if (!is_string($interest) || !isset($interests[$interest])) {
              $this->lock->release($lock_name);
              throw new BadRequestHttpException('Invalid interest provided.');
            }
          }

          $profile->set('field_s_interests', $response['interests']);
          $save_profile = TRUE;
        }

        $field_entity_names = ['ws' => 'work', 'es' => 'education'];
        foreach ($field_entity_names as $field_entity_key => $field_entity_name) {
          $plural_field_entity_name = $field_entity_name . 's';

          if (array_key_exists($plural_field_entity_name, $response)) {
            if (!is_array($response[$plural_field_entity_name])) {
              $this->lock->release($lock_name);
              throw new BadRequestHttpException('The request data is invalid.');
            }

            foreach ($response[$plural_field_entity_name] as $field_entity) {
              $date_start = NULL;
              $date_end = NULL;

              if (
                !is_array($field_entity)
                || !array_key_exists('name', $field_entity)
                || !is_string($field_entity['name'])
                || (
                  array_key_exists('date_start', $field_entity)
                  && !($date_start = \DateTime::createFromFormat(
                      'Y-m-d',
                      $field_entity['date_start'])
                    )
                )
                || (
                  array_key_exists('date_end', $field_entity)
                  && !($date_end = \DateTime::createFromFormat(
                      'Y-m-d',
                      $field_entity['date_end'])
                    )
                )
                || !$date_start
                || ($date_start && $date_end && ($date_start >= $date_end))
              ) {
                $this->lock->release($lock_name);
                throw new BadRequestHttpException('Invalid ' . $field_entity_name . ' provided.');
              }
            }

            foreach ($profile->{"field_s_{$plural_field_entity_name}"}->referencedEntities() as $reference_entity) {
              $reference_entity->delete();
            }

            $profile->set("field_s_{$plural_field_entity_name}", []);

            if (
              is_array($response[$plural_field_entity_name])
              && !empty($response[$plural_field_entity_name])
            ) {
              $paragraph_storage = $this->entityTypeManager->getStorage('paragraph');

              $added_entities = [];
              foreach ($response[$plural_field_entity_name] as $field_entity_data) {
                $added_entities[] = $paragraph_storage->create([
                  'type' => "{$field_entity_name}_student",
                  "field_{$field_entity_key}_name" => ['value' => $field_entity_data['name']],
                  "field_{$field_entity_key}_description" => ['value' => $field_entity_data['description'] ?? NULL],
                  "field_{$field_entity_key}_date_start" => ['value' => $field_entity_data['date_start'] ?? NULL],
                  "field_{$field_entity_key}_date_end" => ['value' => $field_entity_data['date_end'] ?? NULL],
                ]);
              }

              if (!empty($added_entities)) {
                $profile->{"field_s_{$plural_field_entity_name}"} = $added_entities;
              }
            }
            $save_profile = TRUE;
          }
        }
        if (array_key_exists('date_birth', $response)) {
          if (
            !is_string($response['date_birth'])
            || !($date_birth = \DateTime::createFromFormat('Y-m-d', $response['date_birth']))
            || $date_birth > new \DateTime()
          ) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The request data is invalid.');
          }

          $profile->set('field_s_date_birth', $response['date_birth']);
          $save_profile = TRUE;
        }

        if (array_key_exists('short_bio', $response)) {
          if (!is_string($response['short_bio'])) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The request data is invalid.');
          }
          $profile->set('field_s_short_bio', $response['short_bio']);
          $save_profile = TRUE;
        }

        if (array_key_exists('feel_skill_development', $response)) {
          if (!is_string($response['feel_skill_development'])) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The request data is invalid.');
          }

          $profile->set('field_feel_skill_development', $response['feel_skill_development']);
          $save_profile = TRUE;
        }

        if (array_key_exists('nickname', $response)) {
          if (!is_string($response['nickname'])) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The request data is invalid.');
          }

          $ids = ($query = $this->entityTypeManager
            ->getStorage('profile')
            ->getQuery()
          )
            ->condition('uid', $this->currentUser->id(), '!=')
            ->condition(
              $query
                ->orConditionGroup()
                ->condition('field_s_nickname', $response['nickname'])
            )
            ->accessCheck(FALSE)
            ->execute();
          if (!empty($ids)) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The nickname is already taken.');
          }

          if (mb_strlen($response['nickname']) < 3) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The nickname must contain at least 3 characters.');
          }

          if (mb_strlen($response['nickname']) > 30) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The nickname must contain maximum 30 characters.');
          }

          if (!preg_match('/^[a-zA-Z0-9_]+$/', $response['nickname'])) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The nickname must contain only alphanumeric characters or underscore.');
          }

          $profile->set('field_s_nickname', $response['nickname']);
          $user->setUsername($response['nickname']);

          if (array_key_exists('nickname', $response)) {
            $this->sendAccountConfirmationEmail(
              $user->getAccountName(),
              $user->getEmail()
            );
          }

          $save_user = $save_profile = TRUE;
        }

        if (array_key_exists('moto', $response)) {
          if (!is_string($response['moto'])) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The request data is invalid.');
          }

          $profile->set('field_s_moto', $response['moto']);
          $save_profile = TRUE;
        }

        if (
          array_key_exists('current_password', $response)
          || array_key_exists('new_password', $response)
          || array_key_exists('confirm_password', $response)
        ) {
          if (
            !is_string($response['current_password'] ?? NULL)
            || !is_string($response['new_password'] ?? NULL)
            || !is_string($response['confirm_password'] ?? NULL)
          ) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The request data is invalid.');
          }

          if (!preg_match('/^(?=.*[0-9])/', $response['new_password'])) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The password must contain at least 1 number.');
          }

          if (!preg_match('/^(?=.*[a-zA-Z])/', $response['new_password'])) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The password must contain at least 1 letter.');
          }

          if (!preg_match('/^(?=.*[^a-zA-Z0-9])/', $response['new_password'])) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The password must contain at least 1 special character.');
          }

          if (!preg_match('/^(?=.{8,32}$)/', $response['new_password'])) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The password must be between 8 and 32 characters.');
          }

          if ($response['new_password'] !== $response['confirm_password']) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('Passwords do not match.');
          }

          if (!$this->passwordChecker->check($response['current_password'], $user->getPassword() ?? '')) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The current password is wrong.');
          }

          $user->setPassword($response['new_password']);
          $save_user = TRUE;
        }

        if (array_key_exists('is_app_rated', $response) && is_bool($response['is_app_rated'])) {
          $additional_data = $profile->field_s_additional_data->isEmpty()
            ? []
            : unserialize($profile->field_s_additional_data->value, ['allowed_classes' => FALSE]);

          if ((bool) $additional_data['is_app_rated'] !== $response['is_app_rated']) {
            $additional_data['is_app_rated'] = $response['is_app_rated'];
            $profile->set('field_s_additional_data', serialize($additional_data));

            $save_profile = TRUE;
          }
        }

        if (array_key_exists('notifications', $response)) {
          if (
            !is_array($response['notifications'])
            || !array_key_exists('push', $response['notifications'])
            || !array_key_exists('email', $response['notifications'])
            || !is_array($response['notifications']['push'])
            || !is_array($response['notifications']['email'])
            || !is_bool($response['notifications']['push']['status'])
            || !array_key_exists('subscription_id', $response['notifications']['push'])
            || !is_string($response['notifications']['push']['subscription_id'])
            || !is_bool($response['notifications']['email']['status'])
          ) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The request data is invalid.');
          }

          $profile->set(
            'field_s_notification_type_push',
            (bool) $response['notifications']['push']['status']
          );

          $profile->set(
            'field_s_notification_type_email',
            (bool) $response['notifications']['email']['status']
          );

          if ((bool) $response['notifications']['push']['status']) {
            $result = $this->oneSignal->subscribeUser(
              $user,
              $response['notifications']['push']['subscription_id']
            );
            if (!$result) {
              $this->lock->release($lock_name);
              throw new BadRequestHttpException('Could not subscribe user for push notifications. Please try again later.');
            }
          }
          else {
            $result = $this->oneSignal->unsubscribeUser(
              $user,
              $response['notifications']['push']['subscription_id']
            );

            if (!$result) {
              $this->lock->release($lock_name);
              throw new BadRequestHttpException('Could not unsubscribe user from push notifications. Please try again later.');
            }
          }

          $save_profile = TRUE;
        }

        if (array_key_exists('public_sharing_config', $response)) {
          if (!is_array($response['public_sharing_config'])) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The request data is invalid.');
          }

          $required_keys = [
            'show_branches',
            'show_trophies',
            'show_brayn_scan',
            'show_ambitions',
            'show_education',
            'show_work',
            'show_projects',
          ];

          // Check if all required keys exist and are boolean.
          foreach ($required_keys as $key) {
            if (!array_key_exists($key, $response['public_sharing_config'])
              || !is_bool($response['public_sharing_config'][$key])
            ) {
              $this->lock->release($lock_name);
              throw new BadRequestHttpException('The request data is invalid.');
            }
          }

          // Check if there are any extra keys.
          if (count(array_diff(array_keys($response['public_sharing_config']), $required_keys)) > 0) {
            $this->lock->release($lock_name);
            throw new BadRequestHttpException('The request data is invalid.');
          }

          $profile->set('field_public_sharing_config', serialize($response['public_sharing_config']));
          $save_profile = TRUE;
        }

        if (!empty($response['nano_boost_data'])) {
          if ($this->nanoboostDataService->updateUserNanoboostData($response['nano_boost_data'], $this->currentUser->id(), $profile)) {
            $save_profile = TRUE;
          }
        }

        if ($save_profile) {
          $profile->save();
        }

        if ($save_user) {
          $user->save();
        }

        $this->lock->release($lock_name);
        break;
      }
      else {
        // Couldn't acquire the lock, wait and try again.
        $attempt++;
        usleep(100000);
      }
    }

    if ($attempt === $max_attempts) {
      $this->logger->warning('Concurrent profile save blocked.');
      throw new BadRequestHttpException('The request could not be processed. Please try again later.');
    }

    return new ModifiedResourceResponse(TRUE);
  }

  /**
   * Send account confirmation email.
   *
   * @param string $username
   *   The user name.
   * @param string $email
   *   The user email.
   */
  private function sendAccountConfirmationEmail(string $username, string $email): void {
    $result = $this->mailManager->mail(
      'bw_mailing',
      'email_account_confirmation',
      $email,
      $this->languageManager->getCurrentLanguage()->getId(),
      ['name' => $username],
    );
    if (!$result['result']) {
      $message = $this->t('There was a problem sending your email notification to @email.', ['@email' => $email]);
      $this->logger->error($message);

      return;
    }

    $message = $this->t('An email notification has been sent to @email.', ['@email' => $email]);
    $this->logger->notice($message);
  }

}
