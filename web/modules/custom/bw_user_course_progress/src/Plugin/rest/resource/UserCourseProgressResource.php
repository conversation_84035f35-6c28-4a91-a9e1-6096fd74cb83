<?php

namespace Drupal\bw_user_course_progress\Plugin\rest\resource;

use Dr<PERSON>al\bw_user_course_progress\Entity\UserCourseProgress;
use <PERSON><PERSON>al\bw_vimeo\VimeoClient;
use <PERSON><PERSON>al\Core\Cache\Cache;
use <PERSON><PERSON>al\Core\Cache\CacheableJsonResponse;
use Drupal\Core\Cache\CacheableMetadata;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Language\LanguageManagerInterface;
use Drupal\Core\Mail\MailManagerInterface;
use Drupal\Core\Session\AccountInterface;
use Dr<PERSON>al\node\NodeInterface;
use Drupal\rest\ModifiedResourceResponse;
use Drupal\rest\Plugin\ResourceBase;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Return the user course progress.
 *
 * @RestResource(
 *   id = "bw_user_course_progress",
 *   label = @Translation("BW: User: Course progress"),
 *   uri_paths = {
 *     "canonical" = "/api/{version}/course-progress/{course_id}"
 *   },
 * )
 */
final class UserCourseProgressResource extends ResourceBase {

  private const REWARDS_PER_STEP_INFO = [
    'course_structure_1' => [
      'steps' => [
        'game_1' => [
          'neurons' => 10,
          'braynbits' => 30,
        ],
        'game_2' => [
          'neurons' => 35,
          'braynbits' => 65,
        ],
        'upload_video' => [
          'neurons' => 70,
          'axons' => 1,
          'braynbits' => 80,
        ],
      ],
      'last_step' => 'upload_video',
      'total' => [
        'neurons' => 115,
        'axons' => 1,
        'braynbits' => 175,
      ],
    ],
    'course_structure_2' => [
      'steps' => [
        'game_1' => [
          'neurons' => 10,
          'braynbits' => 30,
        ],
        'game_2' => [
          'neurons' => 35,
          'braynbits' => 65,
        ],
        'game_3' => [
          'neurons' => 35,
          'braynbits' => 65,
        ],
        'game_4' => [
          'neurons' => 35,
          'braynbits' => 65,
        ],
        'upload_video' => [
          'neurons' => 50,
          'axons' => 1,
          'braynbits' => 50,
        ],
      ],
      'last_step' => 'upload_video',
      'total' => [
        'neurons' => 165,
        'axons' => 1,
        'braynbits' => 275,
      ],
    ],
    'course_structure_3' => [
      'steps' => [
        'game_1' => [
          'neurons' => 10,
          'braynbits' => 30,
        ],
        'game_2' => [
          'neurons' => 5,
          'braynbits' => 20,
        ],
        'game_4' => [
          'neurons' => 50,
          'braynbits' => 100,
        ],
        'game_6' => [
          'neurons' => 50,
          'braynbits' => 100,
        ],
        'game_8' => [
          'neurons' => 50,
          'braynbits' => 100,
        ],
        'game_10' => [
          'neurons' => 50,
          'braynbits' => 100,
        ],
        'game_12' => [
          'neurons' => 50,
          'braynbits' => 100,
        ],
        'game_14' => [
          'neurons' => 50,
          'braynbits' => 100,
        ],
        'game_16' => [
          'neurons' => 50,
          'braynbits' => 100,
        ],
        'upload_video' => [
          'neurons' => 50,
          'axons' => 1,
          'braynbits' => 50,
        ],
      ],
      'last_step' => 'upload_video',
      'total' => [
        'neurons' => 415,
        'axons' => 1,
        'braynbits' => 800,
      ],
    ],
  ];

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  private AccountInterface $currentUser;

  /**
   * Mail manager.
   *
   * @var \Drupal\Core\Mail\MailManagerInterface
   */
  private MailManagerInterface $mailManager;

  /**
   * Language manager.
   *
   * @var \Drupal\Core\Language\LanguageManagerInterface
   */
  private LanguageManagerInterface $languageManager;

  /**
   * Video upload service.
   *
   * @var \Drupal\bw_vimeo\VimeoClient
   */
  private VimeoClient $vimeoClient;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param array $serializer_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   A logger instance.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   * @param \Drupal\Core\Mail\MailManagerInterface $mail_manager
   *   The mail manager.
   * @param \Drupal\Core\Language\LanguageManagerInterface $language_manager
   *   The language manager.
   * @param \Drupal\bw_vimeo\VimeoClient $vimeo_client
   *   Vimeo client service.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    array $serializer_formats,
    LoggerInterface $logger,
    EntityTypeManagerInterface $entity_type_manager,
    AccountInterface $current_user,
    MailManagerInterface $mail_manager,
    LanguageManagerInterface $language_manager,
    VimeoClient $vimeo_client,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
    $this->entityTypeManager = $entity_type_manager;
    $this->currentUser = $current_user;
    $this->mailManager = $mail_manager;
    $this->languageManager = $language_manager;
    $this->vimeoClient = $vimeo_client;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('entity_type.manager'),
      $container->get('current_user'),
      $container->get('plugin.manager.mail'),
      $container->get('language_manager'),
      $container->get('bw_vimeo.client'),
    );
  }

  /**
   * Get the user course progress.
   *
   * @param string $course_id
   *   The course id.
   *
   * @return \Drupal\Core\Cache\CacheableJsonResponse
   *   The HTTP response object.
   */
  public function get(string $course_id): CacheableJsonResponse {
    $node = $this->entityTypeManager->getStorage('node')->load($course_id);
    if (
      !$node
      || !$node->isPublished()
      || !in_array($node->bundle(), _bw_misc_get_course_node_types())
    ) {
      throw new NotFoundHttpException('The course could not be found or is not published.');
    }

    $response = [
      'project_status' => NULL,
      'status' => 'not_started',
      'current_step' => 'intro_video',
      'next_step' => $this->getNextAllowedStep($node, 'intro_video'),
      'has_access' => FALSE,
    ];

    $access_tests = $this->entityTypeManager
      ->getStorage('course_progress_access_test')
      ->loadByProperties([
        'uid' => $this->currentUser->id(),
        'cid' => $node->id(),
      ]);

    if (
      !$node->field_cs1_payment_type->isEmpty()
      && in_array(
        $node->field_cs1_payment_type->entity->bundle(),
        ['access_money', 'access_neurons', 'access_axons']
      )
    ) {
      $profile = $this->entityTypeManager->getStorage('profile')
        ->loadByUser($this->currentUser, 'student');
      $additional_data = $profile->field_s_additional_data->isEmpty()
        ? []
        : unserialize($profile->field_s_additional_data->value, ['allowed_classes' => FALSE]);
      $bought_courses_data = $additional_data['bought_courses'] ?? [];

      $response['has_access'] = isset($bought_courses_data[$course_id]);
    }

    if (!empty($access_tests)) {
      $response['has_access'] = TRUE;
    }

    $course_progress = $this->entityTypeManager
      ->getStorage('course_progress')
      ->loadByProperties([
        'uid' => $this->currentUser->id(),
        'cid' => $node->id(),
      ]);

    if (($course_progress = $course_progress ? reset($course_progress) : NULL)) {

      $current_step = 'completed' === $course_progress->status->value
        ? NULL
        : $course_progress->current_step->value;
      $response = [
        'project_status' => $course_progress->project_status->value,
        'status' => $course_progress->status->value,
        'current_step' => $current_step,
        'next_step' => 'completed' === $course_progress->status->value
          ? NULL
          : $this->getNextAllowedStep($node, $current_step),
        'rewards' => NULL,
      ] + $response;

      if ('upload_video' === $current_step) {
        $response['video_upload_presigned_url'] = $this->vimeoClient
          ->generateVideoUploadPresignedUploadUrl();
      }

      if (is_null($response['current_step'])) {
        $rewards = [
          'neurons' => self::REWARDS_PER_STEP_INFO[$node->bundle()]['total']['neurons'],
          'axons' => self::REWARDS_PER_STEP_INFO[$node->bundle()]['total']['axons'],
        ];
        foreach ($node->field_cs1_skills->referencedEntities() as $course_skill) {
          $rewards['skills'][$course_skill->field_sc_skill->value] =
            (int) $course_skill->field_sc_braynbits->value;
        }
        $response['rewards'] = $rewards;
      }
    }

    $cache = CacheableMetadata::createFromRenderArray([
      '#cache' => [
        'max-age' => Cache::PERMANENT,
        'tags' => Cache::mergeTags(
          $node->getCacheTags(),
          ["bw_user_course_progress:{$this->currentUser->id()}_{$node->id()}"],
        ),
        'contexts' => ['user'],
      ],
    ]);

    return (new CacheableJsonResponse($response))
      ->addCacheableDependency($cache);
  }

  /**
   * Updates the user course progress.
   *
   * @param string $course_id
   *   The course id.
   * @param \Symfony\Component\HttpFoundation\Request $request
   *   The HTTP request object.
   *
   * @return \Drupal\rest\ModifiedResourceResponse
   *   The HTTP response object.
   */
  public function put(string $course_id, Request $request): ModifiedResourceResponse {
    $node = $this->entityTypeManager->getStorage('node')->load($course_id);
    if (
      !$node
      || !$node->isPublished()
      || !in_array($node->bundle(), _bw_misc_get_course_node_types())
    ) {
      throw new NotFoundHttpException('The course could not be found or is not published.');
    }

    $request_data = json_decode($request->getContent(), TRUE);
    if (!is_array($request_data) || empty($request_data)) {
      throw new BadRequestHttpException('The request data is invalid.');
    }

    if (
      empty($request_data['step'])
      || !is_string($request_data['step'])
      || !in_array(
        $request_data['step'],
        _bw_user_course_progress_get_course_steps()[$node->bundle()],
        TRUE
      )
    ) {
      throw new BadRequestHttpException('The step is empty or invalid.');
    }
    $has_access = TRUE;

    $profile = $this->entityTypeManager->getStorage('profile')
      ->loadByUser($this->currentUser, 'student');

    $course_progress_storage = $this->entityTypeManager
      ->getStorage('course_progress');
    $course_progress = $course_progress_storage->loadByProperties([
      'uid' => $this->currentUser->id(),
      'cid' => $node->id(),
    ]);

    if (empty($course_progress)) {
      throw new BadRequestHttpException('The course progress could not be found.');
    }
    $course_progress = reset($course_progress);

    if ('completed' === $course_progress->status->value) {
      throw new BadRequestHttpException('The course is completed.');
    }

    /*    If ($request_data['step'] !== $course_progress->current_step->value) {
    throw new BadRequestHttpException('The step is invalid.');
    }*/

    if (
      (bool) $profile->field_is_temporary->value
      && 'upload_video' === $request_data['step']
    ) {
      throw new BadRequestHttpException('The step is invalid.');
    }

    $this->saveGameInformation($node, $course_progress, $request_data);

    $course_progress->set(
      'current_step',
      $this->getNextAllowedStep($node, $course_progress->current_step->value)
    );
    $course_progress->set('date_updated', (new \DateTime())->getTimestamp());

    if (!$course_progress->current_step->value) {
      $is_app_rated = (bool) ($additional_data['is_app_rated'] ?? FALSE);

      if (!$is_app_rated) {
        $this->sendRateAppEmail(
          $this->currentUser->getAccountName(),
          $profile->uid->entity->getEmail()
        );
      }
    }
    $course_progress->save();

    Cache::invalidateTags([
      "bw_user_courses:{$this->currentUser->id()}",
      "bw_user_course_progress:{$this->currentUser->id()}_{$node->id()}",
    ]);

    $rewards = $this->returnAndSaveRewards($node, $request_data['step']);

    $response = [
      'project_status' => $course_progress->project_status->value,
      'has_access' => $has_access,
      'status' => $course_progress->status->value,
      'current_step' => 'completed' === $course_progress->status->value
        ? NULL
        : ($current_step = $course_progress->current_step->value),
      'next_step' => 'completed' === $course_progress->status->value
        ? NULL
        : $this->getNextAllowedStep($node, $current_step),
      'rewards' => empty($rewards) ? NULL : $rewards,
    ];

    if ('upload_video' === $current_step) {
      $response['video_upload_presigned_url'] = $this->vimeoClient
        ->generateVideoUploadPresignedUploadUrl();
    }

    return new ModifiedResourceResponse($response);
  }

  /**
   * Get next allowed step.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The node entity.
   * @param string|null $current_step
   *   The current step.
   *
   * @return string|null
   *   The allowed step or null if we reached the last step.
   */
  private function getNextAllowedStep(NodeInterface $node, ?string $current_step): ?string {
    if (is_null($current_step)) {
      return NULL;
    }

    $steps = _bw_user_course_progress_get_course_steps()[$node->bundle()];

    return $steps[array_search($current_step, $steps) + 1] ?? NULL;
  }

  /**
   * Return rewards information and store them into user profile.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The node entity.
   * @param string $current_step
   *   The current step.
   *
   * @return array
   *   Reward information.
   */
  private function returnAndSaveRewards(NodeInterface $node, string $current_step): array {
    $rewards_response = [];

    $profile_storage = $this->entityTypeManager->getStorage('profile');
    $profile = $profile_storage->loadByUser($this->currentUser, 'student');

    // Check if rewards have already been given for upload_video step.
    if ($current_step === 'upload_video') {
      $additional_data = $profile->field_s_additional_data->isEmpty()
        ? []
        : unserialize($profile->field_s_additional_data->value, ['allowed_classes' => FALSE]);

      if (!empty($additional_data['uploaded_video'][$node->id()])) {
        // Rewards have already been given for this course's upload_video step.
        return $rewards_response;
      }
    }

    $rewards_info = self::REWARDS_PER_STEP_INFO[$node->bundle()];
    $steps = _bw_user_course_progress_get_course_steps()[$node->bundle()];
    if ($current_step === end($steps)) {
      $rewards_response['neurons'] = $rewards_info['total']['neurons'];
      $rewards_response['axons'] = $rewards_info['total']['axons'];
      foreach ($node->field_cs1_skills->referencedEntities() as $course_skill) {
        $rewards_response['skills'][$course_skill->field_sc_skill->value] = (int) $course_skill->field_sc_braynbits->value;
      }
    }

    if (empty($rewards_info['steps'][$current_step])) {
      return $rewards_response;
    }

    $profile_skills = [];
    foreach ($profile->field_s_skills->referencedEntities() as $profile_skill) {
      $profile_skills[$profile_skill->field_ss_skill->value] = (int) $profile_skill->field_ss_braynbits->value;
    }

    $course_skills = [];
    foreach ($node->field_cs1_skills->referencedEntities() as $course_skill) {
      $course_skills[$course_skill->field_sc_skill->value] = (int) $course_skill->field_sc_braynbits->value;
    }

    $skills_per_steps = [];
    foreach ($rewards_info['steps'] as $step => $rewards) {
      if ($step !== $rewards_info['last_step']) {
        $percentage = $rewards['braynbits'] / $rewards_info['total']['braynbits'];
        foreach ($course_skills as $course_skill => $course_bb_per_skill) {
          $skills_per_steps[$step][$course_skill] = (int) round($course_bb_per_skill * $percentage);
        }
      }
      else {
        $collected_course_skills = [];
        foreach ($skills_per_steps as $skills_per_step) {
          foreach ($skills_per_step as $skill_per_step => $bb_per_step) {
            $collected_course_skills[$skill_per_step] = $bb_per_step
              + ($collected_course_skills[$skill_per_step] ?? 0);
          }
        }
        foreach ($course_skills as $course_skill => $course_bb_per_skill) {
          $skills_per_steps[$step][$course_skill] = $course_bb_per_skill - ($collected_course_skills[$course_skill] ?? 0);
        }
      }
    }

    if (!($received_skills = $skills_per_steps[$current_step])) {
      return $rewards_response;
    }

    $paragraph_storage = $this->entityTypeManager->getStorage('paragraph');

    foreach ($received_skills as $skill => $bb) {
      $selected_profile_skill = NULL;
      foreach ($profile->field_s_skills->referencedEntities() as $profile_skill) {
        if ($profile_skill->field_ss_skill->value === $skill) {
          $selected_profile_skill = $profile_skill;
        }
      }
      if (!$selected_profile_skill) {
        $selected_profile_skill = $paragraph_storage->create([
          'type' => 'skill_student',
          'field_ss_braynbits' => ['value' => 0],
          'field_ss_skill' => ['value' => $skill],
        ]);
        $profile->field_s_skills[] = $selected_profile_skill;
      }
      $selected_profile_skill->set('field_ss_braynbits', $bb + ((int) $selected_profile_skill->field_ss_braynbits->value));
      $selected_profile_skill->save();

      $rewards_response['skills'][$skill] = $bb;
    }

    $rewards_response['neurons'] = $rewards_info['steps'][$current_step]['neurons'];
    $rewards_response['axons'] = $rewards_info['steps'][$current_step]['axons'] ?? 0;

    $profile->field_s_neurons = ($profile->field_s_neurons->value ?? 0)
      + $rewards_info['steps'][$current_step]['neurons'];
    if (isset($rewards_info['steps'][$current_step]['axons'])) {
      $profile->field_s_axons = $profile->field_s_axons->value + $rewards_info['steps'][$current_step]['axons'];
    }

    if ($current_step === 'upload_video') {
      // Mark that rewards have been given for this course's upload_video step.
      $additional_data = $profile->field_s_additional_data->isEmpty()
        ? []
        : unserialize($profile->field_s_additional_data->value, ['allowed_classes' => FALSE]);
      $additional_data['uploaded_video'][$node->id()] = TRUE;
      $profile->field_s_additional_data = serialize($additional_data);
    }

    $profile->save();

    return $rewards_response;
  }

  /**
   * Save the user-provided game information.
   *
   * @param \Drupal\node\NodeInterface $node
   *   The node entity.
   * @param \Drupal\bw_user_course_progress\Entity\UserCourseProgress $course_progress
   *   The coruse progress entity.
   * @param array $request_data
   *   Request data.
   */
  private function saveGameInformation(
    NodeInterface $node,
    UserCourseProgress $course_progress,
    array $request_data = [],
  ): void {
    if (!str_starts_with($course_progress->current_step->value, 'game_')) {
      return;
    }

    $game = $node
      ->get("field_cs1_{$course_progress->current_step->value}")
      ->entity;
    if (!$game) {
      throw new BadRequestHttpException('The game could not be loaded from the database.');
    }

    if (!is_array($request_data['game_data'])) {
      throw new BadRequestHttpException('The game data format is invalid.');
    }

    $game_responses = json_decode(
      $course_progress->game_responses->value ?? '[]',
      TRUE
    );

    switch ($game->bundle()) {
      case 'quiz_crossword':
        $has_error = FALSE;

        if (!isset($game_responses[$course_progress->current_step->value]['attempts'])) {
          $game_responses[$course_progress->current_step->value]['attempts'] = 0;
        }

        $question = $game->get('field_cq_question')->value;

        if (
          empty($request_data['game_data'])
          || !is_array($request_data['game_data']['letters'])
          || !is_string($request_data['game_data']['question'])
          || empty($request_data['game_data']['letters'])
          || count($request_data['game_data']['letters']) > 10
          || empty($request_data['game_data']['question'])
          || array_reduce($request_data['game_data']['letters'], fn($carry, $letter) => $carry || (is_string($letter) && strlen($letter) > 1))
          || $request_data['game_data']['question'] !== $question
        ) {
          throw new BadRequestHttpException('The crossword format is invalid.');
        }
        $letter_diff = array_diff_assoc(array_map(
          fn($paragraph) => $paragraph->get('field_cq_letter')->value,
          $game->get('field_cq_answer')->referencedEntities()
        ), $request_data['game_data']['letters']);

        if (!empty($letter_diff)) {
          $has_error = TRUE;
          $game_responses[$course_progress->current_step->value]['error_letters'] = $letter_diff;
        }

        if ($has_error) {
          if (2 === $game_responses[$course_progress->current_step->value]['attempts']) {
            $has_error = FALSE;

            unset(
              $game_responses[$course_progress->current_step->value]['attempts'],
              $game_responses[$course_progress->current_step->value]['error_letters']
            );
          }
          else {
            $game_responses[$course_progress->current_step->value]['attempts']++;
          }
          $course_progress->set('game_responses', json_encode($game_responses));
          $course_progress->save();

          if ($has_error) {
            throw new BadRequestHttpException('Oops! You have an error :(');
          }
        }
        else {
          unset($game_responses[$course_progress->current_step->value]['attempts']);
        }

        $course_progress->set('game_responses', json_encode($game_responses));

        break;

      case 'quiz_image':
        $questions = array_map(
          fn($paragraph) => $paragraph->get('field_iq_question_title')->value,
          $game->get('field_iq_question')->referencedEntities()
        );
        foreach ($request_data['game_data'] as $question) {
          if (
            empty($question)
            || !is_array($question)
            || empty($question['question'])
            || empty($question['answer'])
            || !is_string($question['question'])
            || !is_string($question['answer'])
            || FALSE === array_search($question['question'], $questions)
          ) {
            throw new BadRequestHttpException('The questions format is invalid.');
          }

          unset($questions[array_search($question['question'], $questions)]);
        }
        if (!empty($questions)) {
          throw new BadRequestHttpException('One of the questions is not answered.');
        }
        break;

      case 'quiz_video':
        $questions = array_map(
          fn($paragraph) => $paragraph->get('field_vq_question_title')->value,
          $game->get('field_vq_question')->referencedEntities()
        );
        foreach ($request_data['game_data'] as $question) {
          if (
            empty($question)
            || !is_array($question)
            || empty($question['question'])
            || empty($question['answer'])
            || !is_string($question['question'])
            || !is_string($question['answer'])
            || FALSE === array_search($question['question'], $questions)
          ) {
            throw new BadRequestHttpException('The questions format is invalid.');
          }

          unset($questions[array_search($question['question'], $questions)]);
        }
        if (!empty($questions)) {
          throw new BadRequestHttpException('One of the questions is not answered.');
        }
        break;

      case 'quiz_tinder_mechanics':
        $questions = array_map(
          fn($paragraph) => $paragraph->get('field_tq_question_title')->value,
          $game->get('field_tq_question')->referencedEntities()
        );
        foreach ($request_data['game_data'] as $question) {
          if (
            empty($question)
            || !is_array($question)
            || empty($question['question'])
            || empty($question['answer'])
            || !is_string($question['question'])
            || !is_string($question['answer'])
            || FALSE === array_search($question['question'], $questions)
          ) {
            throw new BadRequestHttpException('The questions format is invalid.');
          }

          unset($questions[array_search($question['question'], $questions)]);
        }
        if (!empty($questions)) {
          throw new BadRequestHttpException('One of the questions is not answered.');
        }
        break;

      default:
        throw new BadRequestHttpException('Unexpected game is selected.');
    }

    $game_responses[$course_progress->current_step->value] = $request_data['game_data'];

    $course_progress->set('game_responses', json_encode($game_responses));
  }

  /**
   * Send Rate App email.
   *
   * @param string $username
   *   The user name.
   * @param string $email
   *   The user email.
   */
  protected function sendRateAppEmail(string $username, string $email): void {
    $result = $this->mailManager->mail(
      'bw_mailing',
      'email_rate_app',
      $email,
      $this->languageManager->getCurrentLanguage()->getId(),
      ['name' => $username],
    );
    if (!$result['result']) {
      $message = $this->t('There was a problem sending email notification to @email.', ['@email' => $email]);
      $this->logger->error($message);

      return;
    }

    $message = $this->t('An email notification has been sent to @email.', ['@email' => $email]);
    $this->logger->notice($message);
  }

}
