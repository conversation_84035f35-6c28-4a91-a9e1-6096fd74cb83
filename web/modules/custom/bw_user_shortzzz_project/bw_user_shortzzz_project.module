<?php

/**
 * @file
 * Module-related hooks.
 */

use <PERSON><PERSON>al\bw_user_shortzzz_project\Entity\UserShortzzzProject;
use <PERSON><PERSON>al\Core\Cache\Cache;
use Drupal\Core\StringTranslation\TranslatableMarkup;
use <PERSON><PERSON><PERSON>\node\NodeInterface;
use <PERSON><PERSON><PERSON>\user\UserInterface;

/**
 * Implements hook_views_data_alter().
 */
function bw_user_shortzzz_project_views_data_alter(array &$data) {
  $data['users_field_data']['shortzzz_project_relationship'] = [
    'title' => new TranslatableMarkup('Student shortzzz project'),
    'help' => new TranslatableMarkup('Student shortzzz project relationship'),
    'relationship' => [
      'group' => new TranslatableMarkup('User shortzzz project'),
      'label' => new TranslatableMarkup('User shortzzz project'),
      'base' => 'bw_shortzzz_project',
      'base field' => 'uid',
      'relationship field' => 'uid',
      'id' => 'standard',
    ],
  ];
  $data['shortzzz_project_relationship']['response'] = [
    'title' => new TranslatableMarkup('Response'),
    'help' => new TranslatableMarkup('The response of the shortzzz project.'),
    'field' => [
      'id' => 'shortzzz_project',
    ],
  ];
}

/**
 * Implements hook_cron().
 */
function bw_user_shortzzz_project_cron() {
  $queue = \Drupal::queue('bw_user_shortzzz_project_queue');

  $results = \Drupal::entityTypeManager()
    ->getStorage('shortzzz_project')
    ->getQuery()
    ->accessCheck(FALSE)
    ->condition('status', ['banned', 'declined'], 'IN')
    ->condition(
      'date_updated',
      (new \DateTime())->modify('- 72 hours')->getTimestamp(),
      '<',
    )
    ->execute();
  foreach ($results as $result) {
    $queue->createItem(['id' => $result]);
  }
}

/**
 * Implements hook_entity_delete().
 */
function bw_user_shortzzz_project_entity_delete($entity) {
  $shortzzz_project_storage = \Drupal::entityTypeManager()
    ->getStorage('shortzzz_project');
  $vimeo_client = \Drupal::service('bw_vimeo.client');
  $logger = \Drupal::service('logger.factory');
  $do_delete_shortzzz_project = FALSE;

  $shortzzz_projects = [];
  if ($entity instanceof UserShortzzzProject) {
    $shortzzz_projects[] = $entity;
  }
  if ($entity instanceof UserInterface) {
    $do_delete_shortzzz_project = TRUE;
    $shortzzz_projects = $shortzzz_project_storage
      ->loadByProperties(['uid' => $entity->id()]);
  }
  if ($entity instanceof NodeInterface) {
    $do_delete_shortzzz_project = TRUE;
    $shortzzz_projects = $shortzzz_project_storage
      ->loadByProperties(['sid' => $entity->id()]);
  }

  if (empty($shortzzz_projects)) {
    return;
  }

  foreach ($shortzzz_projects as $shortzzz_project) {
    $delete_response = $vimeo_client->delete($shortzzz_project->uri->value);
    if (204 !== $delete_response['status']) {
      $params = [
        '@uri' => $shortzzz_project->uri->value,
        '@error' => $delete_response['body']['error'] ?? 'Undefined',
      ];
      $logger->get('shortzzz_project')
        ->error('Error deleting video: @uri: @error', $params);
    }

    $path = explode('/', trim(parse_url($shortzzz_project->uri->value, PHP_URL_PATH), '/'));
    $uri = "/videos/{$path[0]}";

    \Drupal::database()
      ->delete('bw_vimeo_video_data')
      ->condition('uri', $uri)
      ->execute();

    if (!is_null($shortzzz_project->preview_image_fid->entity)) {
      \Drupal::entityTypeManager()
        ->getStorage('file')
        ->delete([$shortzzz_project->preview_image_fid->entity]);
    }

    if ($do_delete_shortzzz_project) {
      $shortzzz_project->delete();
    }
  }
}

/**
 * Implements hook_ENTITY_TYPE_update().
 */
function bw_user_shortzzz_project_shortzzz_project_update(UserShortzzzProject $entity) {
  // Get the original entity before the changes.
  /** @var \Drupal\bw_user_shortzzz_project\Entity\UserShortzzzProject $original_entity */
  $original_entity = \Drupal::entityTypeManager()->getStorage('shortzzz_project')->loadUnchanged($entity->id());
  $original_status = $original_entity->get('status')->value;
  $new_status = $entity->get('status')->value;

  // Check if the status has changed from 'featured' to something else.
  if ($original_status == 'featured' && $new_status != 'featured') {
    // Invalidate the cache tag.
    Cache::invalidateTags(['bw_user_feed_tag']);
  }
}
