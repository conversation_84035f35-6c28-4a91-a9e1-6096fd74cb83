<?php

namespace <PERSON><PERSON>al\bw_user_shortzzz_project\Plugin\QueueWorker;

use <PERSON><PERSON><PERSON>\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Plugin\ContainerFactoryPluginInterface;
use <PERSON><PERSON>al\Core\Queue\QueueWorkerBase;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Processes items from the bw_user_shortzzz_project_queue.
 *
 * @QueueWorker(
 *   id = "bw_user_shortzzz_project_queue",
 *   title = @Translation("User Shortzzz Project Queue Worker"),
 *   cron = {"time" = 120}
 * )
 */
final class UserShortzzzProjectQueueWorker extends QueueWorkerBase implements ContainerFactoryPluginInterface {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin ID for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   Entity type manager.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    array $plugin_definition,
    EntityTypeManagerInterface $entity_type_manager,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition);
    $this->entityTypeManager = $entity_type_manager;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->get('entity_type.manager'),
    );
  }

  /**
   * {@inheritdoc}
   */
  public function processItem($data) {
    $project = $this->entityTypeManager
      ->getStorage('shortzzz_project')
      ->load($data['id']);
    if (!$project) {
      return;
    }

    $status = $project->status->value;
    $user = $project->uid->entity;

    $project->delete();

    if (!$user) {
      return;
    }

    /** @var \Drupal\profile\ProfileStorage $profile_storage */
    $profile_storage = \Drupal::entityTypeManager()->getStorage('profile');
    $profile = $profile_storage->loadByUser($user, 'student');

    if ('banned' === $status) {
      $additional_data = !$profile->field_s_additional_data->isEmpty()
        ? unserialize($profile->field_s_additional_data->value, ['allowed_classes' => FALSE])
        : [];

      if (
        !isset($additional_data['banned'])
        || $additional_data['banned']['expire'] < (new \DateTime())->getTimestamp()
      ) {
        $additional_data['banned'] = [
          'count' => 1,
          'expire' => (new \DateTime())->modify('+ 30 days')->getTimestamp(),
        ];
      }
      elseif ($additional_data['banned']['expire'] > (new \DateTime())->getTimestamp()) {
        ++$additional_data['banned']['count'];

        if (3 === $additional_data['banned']['count']) {
          $additional_data['banned']['count'] = 0;
          $additional_data['access_test_lock'] = [
            'expire' => (new \DateTime())->modify('+ 7 days')->getTimestamp(),
          ];
        }
      }

      $profile->set('field_s_additional_data', serialize($additional_data));
      $profile->save();
    }
  }

}
