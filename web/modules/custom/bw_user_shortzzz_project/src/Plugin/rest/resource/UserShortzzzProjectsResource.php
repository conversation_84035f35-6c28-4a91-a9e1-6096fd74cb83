<?php

namespace Drupal\bw_user_shortzzz_project\Plugin\rest\resource;

use Drupal\bw_requests\RequestsService;
use <PERSON><PERSON>al\Core\Cache\Cache;
use <PERSON><PERSON>al\Core\Cache\CacheableJsonResponse;
use <PERSON><PERSON>al\Core\Cache\CacheableMetadata;
use Drupal\Core\Database\Connection;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\rest\Plugin\ResourceBase;
use Drupal\search_api\Query\QueryInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Return the user shortzzz projects.
 *
 * @RestResource(
 *   id = "bw_user_shortzzz_projects",
 *   label = @Translation("BW: User: Shortzzz projects"),
 *   uri_paths = {
 *     "canonical" = "/api/{version}/user/{user_id}/shortzzz-projects",
 *   },
 * )
 */
final class UserShortzzzProjectsResource extends ResourceBase {

  /**
   * The entity type manager.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  private EntityTypeManagerInterface $entityTypeManager;

  /**
   * The database connection.
   *
   * @var \Drupal\Core\Database\Connection
   */
  private Connection $connection;

  /**
   * The requests service.
   *
   * @var \Drupal\bw_requests\RequestsService
   */
  private RequestsService $requestsService;

  /**
   * Class constructor.
   *
   * @param array $configuration
   *   A configuration array containing information about the plugin instance.
   * @param string $plugin_id
   *   The plugin_id for the plugin instance.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param array $serializer_formats
   *   The available serialization formats.
   * @param \Psr\Log\LoggerInterface $logger
   *   A logger instance.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Database\Connection $connection
   *   The database connection.
   * @param \Drupal\bw_requests\RequestsService $requests_service
   *   The requests service.
   */
  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    array $serializer_formats,
    LoggerInterface $logger,
    EntityTypeManagerInterface $entity_type_manager,
    Connection $connection,
    RequestsService $requests_service,
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
    $this->entityTypeManager = $entity_type_manager;
    $this->connection = $connection;
    $this->requestsService = $requests_service;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('entity_type.manager'),
      $container->get('database'),
      $container->get('bw_requests'),
    );
  }

  /**
   * Get the user shortzzz projects.
   *
   * @param string $user_id
   *   The user id.
   *
   * @return \Drupal\Core\Cache\CacheableJsonResponse
   *   The HTTP response object.
   */
  public function get(string $user_id): CacheableJsonResponse {
    /** @var \Drupal\user\Entity\User $user */
    $user = $this->entityTypeManager->getStorage('user')->load($user_id);
    if (
      !$user
      || !$user->isActive()
    ) {
      throw new NotFoundHttpException('The user could not be found or is not active.');
    }

    if (!in_array('student', $user->getRoles(), TRUE)) {
      throw new BadRequestHttpException('Only student videos could be returned.');
    }

    $render_array = [
      '#cache' => [
        'max-age' => 5 * 60,
        'tags' => Cache::mergeTags(
          $user->getCacheTags(),
          ["bw_user_shortzzz_projects:{$user->id()}"],
        ),
      ],
    ];

    // Fetch shortzzz projects.
    /** @var \Drupal\search_api\Entity\Index $index */
    $index = $this->entityTypeManager->getStorage('search_api_index')->load('default');
    $query = $index->query()
      ->addCondition('search_api_datasource', 'entity:shortzzz_project')
      ->addCondition('uspj_user_id', $user->id())
      ->addCondition('uspj_status', ['approved', 'featured'], 'IN')
      ->sort('uspj_id', QueryInterface::SORT_DESC);

    $project_ids = array_values(
      array_map(
        fn ($result) => $result->getField('uspj_id')->getValues()[0],
        $query->execute()->getResultItems()
      )
    );

    if (!empty($project_ids)) {
      $flagged_project_ids = $this->connection
        ->select('bw_flag_counts_shortzzz_project_like')
        ->fields('bw_flag_counts_shortzzz_project_like', ['entity_id', 'count'])
        ->condition('entity_id', $project_ids, 'IN')
        ->execute()
        ->fetchAll(\PDO::FETCH_KEY_PAIR);
      if (!empty($flagged_project_ids)) {
        arsort($flagged_project_ids);
        $most_liked_project_ids = array_keys($flagged_project_ids);
        $least_liked_project_ids = array_diff($project_ids, $most_liked_project_ids);
        rsort($least_liked_project_ids);
        $project_ids = [
          ...$most_liked_project_ids,
          ...$least_liked_project_ids,
        ];
      }
    }

    $project_urls = array_values(
      array_map(
        fn ($project_id) => "/api/v2/shortzzz-project/{$project_id}",
        $project_ids
      )
    );

    $results = !empty($project_urls)
      ? array_values(array_filter($this->requestsService->send($project_urls)))
      : [];

    return (new CacheableJsonResponse($results))
      ->addCacheableDependency(CacheableMetadata::createFromRenderArray($render_array));
  }

}
