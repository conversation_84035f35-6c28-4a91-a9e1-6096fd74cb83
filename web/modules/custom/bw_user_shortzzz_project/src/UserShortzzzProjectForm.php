<?php

namespace Drupal\bw_user_shortzzz_project;

use <PERSON><PERSON>al\bw_nanoboost_progress\NanoFrameworkRewardService;
use Drupal\bw_nanoboost_progress\Plugin\rest\resource\NanoBoostProgressResource;
use <PERSON><PERSON>al\bw_notifications\NotificationService;
use <PERSON><PERSON>al\bw_vimeo\VimeoClient;
use Drupal\Component\Datetime\TimeInterface;
use Drupal\Core\Cache\Cache;
use Drupal\Core\Entity\ContentEntityForm;
use Drupal\Core\Entity\EntityFieldManagerInterface;
use Drupal\Core\Entity\EntityRepositoryInterface;
use Drupal\Core\Entity\EntityTypeBundleInfoInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Language\LanguageManagerInterface;
use Drupal\Core\Mail\MailManagerInterface;
use Drupal\file\FileInterface;
use Drupal\file\FileUsage\FileUsageInterface;
use <PERSON><PERSON>al\node\NodeInterface;
use <PERSON>upal\user\UserInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Form handler for the shortzzz project edit forms.
 */
final class UserShortzzzProjectForm extends ContentEntityForm {

  /**
   * The shortzzz project entity.
   *
   * @var \Drupal\bw_user_shortzzz_project\UserShortzzzProject
   */
  protected $entity;

  /**
   * Vimeo client.
   *
   * @var \Drupal\bw_vimeo\VimeoClient
   */
  protected VimeoClient $vimeoClient;

  /**
   * Notification service.
   *
   * @var \Drupal\bw_notifications\NotificationService
   */
  protected NotificationService $notification;

  /**
   * The entity field manager.
   *
   * @var \Drupal\Core\Entity\EntityFieldManagerInterface
   */
  protected EntityFieldManagerInterface $entityFieldManager;

  /**
   * Mail manager.
   *
   * @var \Drupal\Core\Mail\MailManagerInterface
   */
  protected MailManagerInterface $mailManager;

  /**
   * Language manager.
   *
   * @var \Drupal\Core\Language\LanguageManagerInterface
   */
  protected LanguageManagerInterface $languageManager;

  /**
   * The file usage interface.
   *
   * @var \Drupal\file\FileUsage\FileUsageInterface
   */
  protected FileUsageInterface $fileUsage;

  /**
   * The nano-framework reward service.
   *
   * @var \Drupal\bw_nanoboost_progress\NanoFrameworkRewardService
   */
  protected NanoFrameworkRewardService $nanoFrameworkRewardService;

  /**
   * Class constructor.
   *
   * @param \Drupal\Core\Entity\EntityRepositoryInterface $entity_repository
   *   The entity repository service.
   * @param \Drupal\Core\Entity\EntityTypeBundleInfoInterface $entity_type_bundle_info
   *   The entity type bundle service.
   * @param \Drupal\Component\Datetime\TimeInterface $time
   *   The time service.
   * @param \Drupal\bw_vimeo\VimeoClient $vimeo_client
   *   Vimeo client.
   * @param \Drupal\bw_notifications\NotificationService $notification
   *   Notification service.
   * @param \Drupal\Core\Entity\EntityFieldManagerInterface $entity_field_manager
   *   The entity field manager.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The entity type manager.
   * @param \Drupal\Core\Mail\MailManagerInterface $mail_manager
   *   The mail manager.
   * @param \Drupal\Core\Language\LanguageManagerInterface $language_manager
   *   The language manager.
   * @param \Drupal\file\FileUsage\FileUsageInterface $file_usage
   *   File usage interface.
   * @param \Drupal\bw_nanoboost_progress\NanoFrameworkRewardService $nano_framework_reward_service
   *   The nano-framework reward service.
   */
  public function __construct(
    EntityRepositoryInterface $entity_repository,
    EntityTypeBundleInfoInterface $entity_type_bundle_info,
    TimeInterface $time,
    VimeoClient $vimeo_client,
    NotificationService $notification,
    EntityFieldManagerInterface $entity_field_manager,
    EntityTypeManagerInterface $entity_type_manager,
    MailManagerInterface $mail_manager,
    LanguageManagerInterface $language_manager,
    FileUsageInterface $file_usage,
    NanoFrameworkRewardService $nano_framework_reward_service,
  ) {
    $this->entityRepository = $entity_repository;
    $this->entityTypeBundleInfo = $entity_type_bundle_info;
    $this->time = $time;
    $this->vimeoClient = $vimeo_client;
    $this->notification = $notification;
    $this->entityFieldManager = $entity_field_manager;
    $this->entityTypeManager = $entity_type_manager;
    $this->mailManager = $mail_manager;
    $this->languageManager = $language_manager;
    $this->fileUsage = $file_usage;
    $this->nanoFrameworkRewardService = $nano_framework_reward_service;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('entity.repository'),
      $container->get('entity_type.bundle.info'),
      $container->get('datetime.time'),
      $container->get('bw_vimeo.client'),
      $container->get('bw_notification'),
      $container->get('entity_field.manager'),
      $container->get('entity_type.manager'),
      $container->get('plugin.manager.mail'),
      $container->get('language_manager'),
      $container->get('file.usage'),
      $container->get('bw_nanoboost_progress.nano_framework_reward_service'),
    );
  }

  /**
   * {@inheritdoc}
   */
  public function form(array $form, FormStateInterface $form_state) {
    $form = parent::form($form, $form_state);

    $form['#title'] = $this->t('Edit shortzzz project %label', ['%label' => $this->entity->label()]);

    if (($shortzzz = $this->entity->sid->entity) instanceof NodeInterface) {
      $form['shortzzz'] = [
        '#type' => 'entity_autocomplete',
        '#target_type' => 'node',
        '#title' => $this->t('shortzzz'),
        '#tags' => TRUE,
        '#default_value' => $shortzzz,
        '#selection_handler' => 'default',
        '#disabled' => TRUE,
      ];
    }
    if (($user = $this->entity->uid->entity) instanceof UserInterface) {
      $form['user'] = [
        '#type' => 'entity_autocomplete',
        '#target_type' => 'user',
        '#title' => $this->t('User'),
        '#tags' => TRUE,
        '#default_value' => $user,
        '#selection_handler' => 'default',
        '#disabled' => TRUE,
      ];
    }

    $form['new_title'] = [
      '#type' => 'textarea',
      '#title' => $this->t('Title'),
      '#rows' => 2,
      '#maxlength' => 100,
      '#default_value' => $this->entity->title->value,
    ];

    $form['likes'] = [
      '#type' => 'number',
      '#title' => $this->t('Initial likes'),
      '#min' => '0',
      '#default_value' => $this->entity->initial_likes->value ?? 0,
    ];

    $form['new_video'] = [
      '#type' => 'url',
      '#title' => $this->t('New Video URL'),
      '#description' => $this->t('Enter the URL.'),
      '#default_value' => $this->entity->uri->value,
    ];

    if (
      !$this->entity->uri->isEmpty()
      && ($video_info = $this->vimeoClient->videoInfo($this->entity->uri->value))
      && !empty($video_info['embedUrl'])
      && !empty($video_info['width'])
      && !empty($video_info['height'])
    ) {
      $width = $video_info['width'];
      $height = $video_info['height'];
      if ($height > 350) {
        $width = $width * (350 / $height);
        $height = 350;
      }

      $form['video'] = [
        '#type' => 'inline_template',
        '#title' => $this->t('Video'),
        '#template' => '<iframe src="{{src}}" width="{{width}}" height="{{height}}" frameborder="0" allowfullscreen></iframe>',
        '#context' => [
          'src' => $video_info['embedUrl'],
          'width' => $width,
          'height' => $height,
        ],
      ];

      $date = (new \DateTime())->format('Y-m');

      $form['image'] = [
        '#type' => 'managed_file',
        '#title' => $this->t('Preview image'),
        '#description' => $this->t('Allowed file types: jpg, jpeg, png'),
        '#upload_validators' => [
          'file_validate_extensions' => ['jpg jpeg png'],
        ],
        '#upload_location' => "s3://user_shortzzz/{$date}/",
      ];
    }
    else {
      $form['video'] = [
        '#type' => 'markup',
        '#markup' => '<p>No video found.</p>',
      ];
    }

    if (!is_null($this->entity->preview_image_fid->entity)) {
      $form['image']['#default_value'] = [(int) $this->entity->preview_image_fid->entity->id()];

      $form['preview_image'] = [
        '#type' => 'markup',
        '#markup' => "<img src='{$this->entity->preview_image_fid->entity->createFileUrl()}'/>",
      ];
    }
    elseif (!empty($video_info)) {
      $form['preview_image'] = [
        '#type' => 'markup',
        '#markup' => "<img src='{$video_info['uriImage']}' width='{$width}' height='{$height}'/>",
      ];
    }

    $fields = $this->entityFieldManager
      ->getFieldStorageDefinitions('shortzzz_project', 'shortzzz_project');
    $form['new_status'] = [
      '#type' => 'select',
      '#title' => $this->t('Status'),
      '#options' => options_allowed_values($fields['status']),
      '#default_value' => $this->entity->status->value,
      '#disabled' => ('banned' === $this->entity->status->value) && !$video_info,
    ];

    $form['hide_feed'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Hide from feed'),
      '#default_value' => $this->entity->hide_feed->value,
      '#on_label' => $this->t('Hide'),
      '#off_label' => $this->t('Show'),
    ];

    $form['delete']['delete'] = [
      '#type' => 'submit',
      '#value' => $this->t('Delete'),
      '#submit' => ['::deleteShortzzzProject'],
      '#button_type' => 'danger',
      '#weight' => 99,
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function deleteShortzzzProject(array &$form, FormStateInterface $form_state) {
    $shortzzz_project = $this->entity;

    // Perform any additional operations before deleting, if necessary.
    // Delete the entity.
    $shortzzz_project->delete();

    // Display a message to the user.
    $this->messenger()->addMessage($this->t('The shortzzz project has been deleted.'));

    // Redirect the user to a preferred location after deletion.
    $form_state->setRedirect('view.user_shortzzz_projects.page_1');
  }

  /**
   * {@inheritdoc}
   */
  public function save(array $form, FormStateInterface $form_state) {
    $user = $this->entity->uid->entity;

    $status = $form_state->getValue('new_status');
    $title = $form_state->getValue('new_title');
    $initial_likes = $form_state->getValue('likes');
    $hide_feed = $form_state->getValue('hide_feed');

    $shortzzz = $this->entity->sid->entity;

    if (
      (
        'banned' === $this->entity->status->value
        && $this->vimeoClient->videoInfo($this->entity->uri->value)
      )
      || (
        $this->entity->status->value !== $status
        || $this->entity->title->value !== $title
        || $this->entity->initial_likes->value !== $initial_likes
        || $form_state->getValue('image')
        || $form_state->getValue('new_video')
      )
    ) {
      /** @var \Drupal\profile\ProfileStorage $profile_storage */
      $profile_storage = $this->entityTypeManager->getStorage('profile');
      $profile = $profile_storage->loadByUser($user, 'student');

      if (
        ('approved' === $status || 'featured' === $status)
        && (bool) $profile->field_s_notification_type_email->value
      ) {
        $this->sendProjectEmail(
          $status,
          $user->getAccountName(),
          $user->getEmail()
        );
      }

      if ('approved' === $status) {
        $this->notification->send(
          $profile,
          [
            'contents' => ['en' => $this->t("🎉 Your project was approved. It's a hit!")],
            'include_external_user_ids' => [$user->uuid()],
            'data' => [
              'type' => 'shortzzz_project_approved',
              'shortzzz_project_id' => $this->entity->id(),
            ],
          ]
        );
      }

      if ('featured' === $status) {
        $this->notification->send(
          $profile,
          [
            'contents' => ['en' => $this->t("🏅 Your project made the cut! Congrats on the feature!")],
            'include_external_user_ids' => [$user->uuid()],
            'data' => [
              'type' => 'shortzzz_project_featured',
              'shortzzz_project_id' => $this->entity->id(),
            ],
          ]
        );
      }

      if (
        'declined' === $status
        && 'declined' !== $this->entity->status->value
      ) {
        $this->notification->send(
          $profile,
          [
            'contents' => ['en' => $this->t("😔 Sorry, your video was rejected. No worries—feel free to try again!")],
            'include_external_user_ids' => [$user->uuid()],
            'data' => [
              'type' => 'shortzzz_project_declined',
              'shortzzz_id' => $shortzzz->id(),
            ],
          ]
        );
      }

      if (
        'banned' === $status
        && 'banned' !== $this->entity->status->value
      ) {
        $this->notification->send(
          $profile,
          [
            'contents' => ['en' => $this->t("⚠️ Your video was banned for inappropriate content. Please revise and resubmit.")],
            'include_external_user_ids' => [$user->uuid()],
            'data' => [
              'type' => 'shortzzz_project_banned',
              'shortzzz_id' => $shortzzz->id(),
            ],
          ]
        );
      }

      $this->entity->set('title', $title);
      $this->entity->set('initial_likes', $initial_likes);
      $this->entity->set('hide_feed', $hide_feed);
      $this->entity->set('status', $status);
      $this->entity->set('date_updated', (new \DateTime())->getTimestamp());
      $uri = $form_state->getValue('new_video');
      $this->entity->set('uri', $uri);

      if ($form_state->getValue('image')) {
        $image_file_id = $form_state->getValue('image')[0];

        /** @var \Drupal\file\FileInterface $file */
        $file = $this->entityTypeManager->getStorage('file')->load($image_file_id);
        $file->set('status', FileInterface::STATUS_PERMANENT);
        $file->save();

        $references = $this->fileUsage->listUsage($file);
        $this->fileUsage->add(
          $file,
          'bw_user_shortzzz_project',
          'shortzzz_project',
          $this->entity->id(),
          count($references) + 1
        );

        $this->entity->set('preview_image_fid', $image_file_id);
      }

      if ('approved' === $status || 'featured' === $status) {
        // Award skills, rewards, trophies, and tokens using the shared service.
        $this->nanoFrameworkRewardService->awardNanoFrameworkCompletionRewards($shortzzz, $user->id());

        // Set NB progress to completed if there is a video task component.
        if (
          !$shortzzz->field_components->isEmpty()
          && !$shortzzz
            ->field_components
            ->filter(fn($component) => 'video_task' === $component->entity?->bundle())
            ->isEmpty()
        ) {
          $nanoboost_progress = $this->entityTypeManager->getStorage('nanoboost_progress')->loadByProperties([
            'uid' => $user->id(),
            'nbid' => $shortzzz->id(),
          ]);
          /** @var \Drupal\nanoboost_progress\Entity\NanoBoostProgress $nanoboost_progress */
          $nanoboost_progress = reset($nanoboost_progress);
          if (empty($nanoboost_progress)) {
            /** @var \Drupal\nanoboost_progress\Entity\NanoBoostProgress $nanoboost_progress */
            $nanoboost_progress = $this->entityTypeManager->getStorage('nanoboost_progress')->create([
              'uid' => $user->id(),
              'nbid' => $shortzzz->id(),
            ]);
          }
          $nanoboost_progress->set(
            'status',
            NanoBoostProgressResource::NANOBOOST_STATUS_COMPLETED,
          );
          $nanoboost_progress->save();
        }
      }

      Cache::invalidateTags([
        "bw_user_courses:{$user->id()}",
        "bw_shortzzz_projects:{$this->entity->sid->entity->id()}",
        "bw_shortzzz_project_flags:{$this->entity->id()}",
      ]);

      $this->entity->save();

      $context = [
        '@type' => 'Shortzzz project',
        '%info' => $this->entity->title->value,
        '%new_status' => $this->entity->status->value,
      ];
      $this->logger('bw_user_shortzzz_project')->notice('Updated: @type %info.', $context);
      $this->messenger()->addStatus(
        $this->t('@type %info has been updated.', $context),
      );
    }
  }

  /**
   * Send project featured email.
   *
   * @param string $status
   *   Type of mail based on status.
   * @param string $username
   *   The user name.
   * @param string $email
   *   The user email.
   */
  private function sendProjectEmail(string $status, string $username, string $email): void {
    $result = $this->mailManager->mail(
    'bw_mailing',
    "email_project_{$status}",
    $email,
    $this->languageManager->getCurrentLanguage()->getId(),
    ['name' => $username],
    );
    if (!$result['result']) {
      $message = $this->t('There was a problem sending your email notification to @email.', ['@email' => $email]);
      $this->logger('bw_user_shortzzz_project')->error($message);

      return;
    }

    $message = $this->t('An email notification has been sent to @email.', ['@email' => $email]);
    $this->logger('bw_user_shortzzz_project')->notice($message);
  }

}
